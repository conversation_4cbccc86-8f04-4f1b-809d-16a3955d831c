import * as vscode from 'vscode';
import { CodeParser } from '../utils/parser';
import { FileService } from '../services/fileService';
import { logger } from '../utils/logger';

export class DefinitionProvider implements vscode.DefinitionProvider {
  private fileService: FileService;

  constructor() {
    this.fileService = FileService.getInstance();
  }

  public async provideDefinition(
    document: vscode.TextDocument,
    position: vscode.Position,
    token: vscode.CancellationToken
  ): Promise<vscode.Definition | undefined> {
    try {
      // Get the word at the current position
      const wordRange = document.getWordRangeAtPosition(position);
      if (!wordRange) {
        return undefined;
      }

      const word = document.getText(wordRange);
      if (!word || word.length < 2) {
        return undefined;
      }

      // Parse the current document
      const documentText = document.getText();
      const parsedCode = CodeParser.parseCode(documentText, document.languageId);

      // Look for the definition in the current file first
      const localDefinition = this.findLocalDefinition(word, parsedCode, document.uri);
      if (localDefinition) {
        return localDefinition;
      }

      // Look for the definition in imported files
      const importDefinition = await this.findImportDefinition(word, parsedCode, document);
      if (importDefinition) {
        return importDefinition;
      }

      // Look for the definition in workspace files
      const workspaceDefinition = await this.findWorkspaceDefinition(word, document);
      if (workspaceDefinition) {
        return workspaceDefinition;
      }

      return undefined;

    } catch (error) {
      logger.error('Failed to provide definition:', error as Error);
      return undefined;
    }
  }

  private findLocalDefinition(word: string, parsedCode: any, documentUri: vscode.Uri): vscode.Location | undefined {
    // Look for function definitions
    for (const func of parsedCode.functions) {
      if (func.name === word) {
        const position = new vscode.Position(func.startLine - 1, 0);
        return new vscode.Location(documentUri, position);
      }
    }

    // Look for class definitions
    for (const cls of parsedCode.classes) {
      if (cls.name === word) {
        const position = new vscode.Position(cls.startLine - 1, 0);
        return new vscode.Location(documentUri, position);
      }

      // Look for method definitions within classes
      for (const method of cls.methods) {
        if (method.name === word) {
          const position = new vscode.Position(method.startLine - 1, 0);
          return new vscode.Location(documentUri, position);
        }
      }
    }

    // Look for variable definitions
    for (const variable of parsedCode.variables) {
      if (variable.name === word) {
        const position = new vscode.Position(variable.line - 1, 0);
        return new vscode.Location(documentUri, position);
      }
    }

    return undefined;
  }

  private async findImportDefinition(
    word: string,
    parsedCode: any,
    document: vscode.TextDocument
  ): Promise<vscode.Location | undefined> {
    try {
      // Look through imports to find the module that exports this symbol
      for (const imp of parsedCode.imports) {
        if (imp.imports.includes(word)) {
          const moduleUri = await this.resolveModulePath(imp.module, document);
          if (moduleUri) {
            const moduleContent = await this.fileService.readFile(moduleUri.fsPath);
            const moduleParsedCode = CodeParser.parseCode(moduleContent, CodeParser.getLanguageFromFileName(moduleUri.fsPath));
            
            // Look for the exported symbol in the module
            const definition = this.findExportedSymbol(word, moduleParsedCode, moduleUri);
            if (definition) {
              return definition;
            }
          }
        }
      }

      return undefined;

    } catch (error) {
      logger.error('Failed to find import definition:', error as Error);
      return undefined;
    }
  }

  private async findWorkspaceDefinition(
    word: string,
    document: vscode.TextDocument
  ): Promise<vscode.Location | undefined> {
    try {
      // Search for the symbol in workspace files
      const searchResults = await this.fileService.searchInFiles(
        `\\b${word}\\b`,
        {
          includeFiles: this.getSearchPatterns(document.languageId),
          excludeFiles: ['**/node_modules/**', '**/dist/**', '**/build/**'],
          caseSensitive: true,
          wholeWord: true,
          regex: true,
          maxResults: 50
        }
      );

      // Filter results to find actual definitions (not just usages)
      for (const result of searchResults) {
        if (result.file === document.uri.fsPath) {
          continue; // Skip current file as we already checked it
        }

        try {
          const fileContent = await this.fileService.readFile(result.file);
          const language = CodeParser.getLanguageFromFileName(result.file);
          const parsedCode = CodeParser.parseCode(fileContent, language);
          
          const definition = this.findLocalDefinition(word, parsedCode, vscode.Uri.file(result.file));
          if (definition) {
            return definition;
          }
        } catch (fileError) {
          // Skip files that can't be read
          continue;
        }
      }

      return undefined;

    } catch (error) {
      logger.error('Failed to find workspace definition:', error as Error);
      return undefined;
    }
  }

  private findExportedSymbol(word: string, parsedCode: any, moduleUri: vscode.Uri): vscode.Location | undefined {
    // Look for exported functions, classes, and variables
    for (const exp of parsedCode.exports) {
      if (exp.name === word) {
        // Find the actual definition
        const definition = this.findLocalDefinition(word, parsedCode, moduleUri);
        if (definition) {
          return definition;
        }
      }
    }

    return undefined;
  }

  private async resolveModulePath(moduleName: string, document: vscode.TextDocument): Promise<vscode.Uri | undefined> {
    try {
      const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
      if (!workspaceFolder) {
        return undefined;
      }

      // Handle relative imports
      if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
        const documentDir = vscode.Uri.joinPath(document.uri, '..');
        const resolvedPath = vscode.Uri.joinPath(documentDir, moduleName);
        
        // Try different extensions
        const extensions = this.getFileExtensions(document.languageId);
        for (const ext of extensions) {
          const pathWithExt = vscode.Uri.parse(resolvedPath.toString() + ext);
          try {
            await vscode.workspace.fs.stat(pathWithExt);
            return pathWithExt;
          } catch {
            continue;
          }
        }
      }

      // Handle absolute imports (simplified - would need more sophisticated resolution)
      const srcPath = vscode.Uri.joinPath(workspaceFolder.uri, 'src', moduleName);
      const extensions = this.getFileExtensions(document.languageId);
      for (const ext of extensions) {
        const pathWithExt = vscode.Uri.parse(srcPath.toString() + ext);
        try {
          await vscode.workspace.fs.stat(pathWithExt);
          return pathWithExt;
        } catch {
          continue;
        }
      }

      return undefined;

    } catch (error) {
      logger.error('Failed to resolve module path:', error as Error);
      return undefined;
    }
  }

  private getSearchPatterns(language: string): string[] {
    const patterns: { [key: string]: string[] } = {
      'javascript': ['**/*.js', '**/*.jsx'],
      'typescript': ['**/*.ts', '**/*.tsx'],
      'python': ['**/*.py'],
      'java': ['**/*.java'],
      'csharp': ['**/*.cs'],
      'cpp': ['**/*.cpp', '**/*.hpp', '**/*.h'],
      'c': ['**/*.c', '**/*.h'],
      'go': ['**/*.go'],
      'rust': ['**/*.rs'],
      'php': ['**/*.php'],
      'ruby': ['**/*.rb']
    };

    return patterns[language] || ['**/*'];
  }

  private getFileExtensions(language: string): string[] {
    const extensions: { [key: string]: string[] } = {
      'javascript': ['.js', '.jsx'],
      'typescript': ['.ts', '.tsx', '.js', '.jsx'],
      'python': ['.py'],
      'java': ['.java'],
      'csharp': ['.cs'],
      'cpp': ['.cpp', '.hpp', '.h'],
      'c': ['.c', '.h'],
      'go': ['.go'],
      'rust': ['.rs'],
      'php': ['.php'],
      'ruby': ['.rb']
    };

    return extensions[language] || [''];
  }
}
