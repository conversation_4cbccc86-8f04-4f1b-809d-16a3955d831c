# 200Model8 Extension - Git Setup Script (PowerShell)
Write-Host "🚀 Setting up Git repository for 200Model8 Extension..." -ForegroundColor Green

# Configure Git user (if not already configured)
Write-Host "👤 Configuring Git user..." -ForegroundColor Yellow
git config --global user.name "jeff9497"
git config --global user.email "<EMAIL>"

# Initialize Git repository
Write-Host "📁 Initializing Git repository..." -ForegroundColor Yellow
git init

# Add all files
Write-Host "📝 Adding files to Git..." -ForegroundColor Yellow
git add .

# Create initial commit
Write-Host "💾 Creating initial commit..." -ForegroundColor Yellow
git commit -m "🎉 Initial commit: 200Model8 VS Code Extension with dual AI provider support

Features:
- Dual AI provider support (Puter.js + OpenRouter)
- Comprehensive file operations
- Advanced code generation
- Interactive AI chat interface
- VS Code integration (IntelliSense, hover, code actions)
- Project management and generation
- Multi-language support
- Real-time streaming responses
- Error handling and recovery
- Comprehensive test suite

Ready for development and deployment! 🚀"

# Add remote repository
Write-Host "🌐 Adding remote repository..." -ForegroundColor Yellow
git remote add origin https://<EMAIL>/jeff9497/200Model8Extension.git

# Create and push to main branch
Write-Host "📤 Pushing to GitHub..." -ForegroundColor Yellow
git branch -M main
git push -u origin main

Write-Host "✅ Git setup complete! Repository created at: https://github.com/jeff9497/200Model8Extension" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 Next steps:" -ForegroundColor Cyan
Write-Host "1. Visit: https://github.com/jeff9497/200Model8Extension" -ForegroundColor White
Write-Host "2. Set up branch protection rules (optional)" -ForegroundColor White
Write-Host "3. Configure GitHub secrets for CI/CD (optional)" -ForegroundColor White
Write-Host "4. Start developing! 🎯" -ForegroundColor White
