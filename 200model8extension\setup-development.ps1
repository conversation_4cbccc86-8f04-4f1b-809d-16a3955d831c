# 200Model8 Extension - Complete Development Setup Script (PowerShell)
Write-Host "🚀 200Model8 Extension - Development Setup" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to print colored output
function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Warning($message) {
    Write-Host "⚠️  $message" -ForegroundColor Yellow
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Write-Info($message) {
    Write-Host "ℹ️  $message" -ForegroundColor Blue
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Blue

# Check Node.js
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Success "Node.js is installed: $nodeVersion"
} else {
    Write-Error "Node.js is not installed!"
    Write-Host "Please install Node.js from: https://nodejs.org/"
    exit 1
}

# Check npm
if (Test-Command "npm") {
    $npmVersion = npm --version
    Write-Success "npm is installed: $npmVersion"
} else {
    Write-Error "npm is not installed!"
    exit 1
}

# Check Git
if (Test-Command "git") {
    $gitVersion = git --version
    Write-Success "Git is installed: $gitVersion"
} else {
    Write-Error "Git is not installed!"
    Write-Host "Please install Git from: https://git-scm.com/"
    exit 1
}

# Check VS Code
if (Test-Command "code") {
    Write-Success "VS Code CLI is available"
} else {
    Write-Warning "VS Code CLI not found. Make sure VS Code is installed and added to PATH"
}

Write-Host ""
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue

# Install project dependencies
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Success "Project dependencies installed successfully"
} else {
    Write-Error "Failed to install project dependencies"
    exit 1
}

# Install global tools
Write-Host ""
Write-Host "🛠️  Installing global development tools..." -ForegroundColor Blue

# Install vsce (VS Code Extension Manager)
if (-not (Test-Command "vsce")) {
    npm install -g vsce
    if ($LASTEXITCODE -eq 0) {
        Write-Success "vsce installed globally"
    } else {
        Write-Warning "Failed to install vsce globally. You may need administrator privileges"
    }
} else {
    Write-Success "vsce is already installed"
}

# Install TypeScript globally (optional)
if (-not (Test-Command "tsc")) {
    npm install -g typescript
    if ($LASTEXITCODE -eq 0) {
        Write-Success "TypeScript installed globally"
    } else {
        Write-Warning "Failed to install TypeScript globally"
    }
} else {
    Write-Success "TypeScript is already installed"
}

# Install ESLint globally (optional)
if (-not (Test-Command "eslint")) {
    npm install -g eslint
    if ($LASTEXITCODE -eq 0) {
        Write-Success "ESLint installed globally"
    } else {
        Write-Warning "Failed to install ESLint globally"
    }
} else {
    Write-Success "ESLint is already installed"
}

# Compile TypeScript
Write-Host ""
Write-Host "🔨 Compiling TypeScript..." -ForegroundColor Blue
npm run compile

if ($LASTEXITCODE -eq 0) {
    Write-Success "TypeScript compilation successful"
} else {
    Write-Error "TypeScript compilation failed"
    exit 1
}

# Run linter
Write-Host ""
Write-Host "🔍 Running linter..." -ForegroundColor Blue
npm run lint

if ($LASTEXITCODE -eq 0) {
    Write-Success "Linting passed"
} else {
    Write-Warning "Linting found issues. Check the output above."
}

# Install recommended VS Code extensions
Write-Host ""
Write-Host "🔌 Installing recommended VS Code extensions..." -ForegroundColor Blue

if (Test-Command "code") {
    # List of recommended extensions
    $extensions = @(
        "ms-vscode.vscode-typescript-next",
        "dbaeumer.vscode-eslint",
        "ms-vscode.test-adapter-converter",
        "eamodio.gitlens",
        "ms-vscode.vscode-json"
    )
    
    foreach ($ext in $extensions) {
        code --install-extension $ext --force
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Installed extension: $ext"
        } else {
            Write-Warning "Failed to install extension: $ext"
        }
    }
} else {
    Write-Warning "VS Code CLI not available. Please install extensions manually:"
    Write-Host "  - TypeScript and JavaScript Language Features"
    Write-Host "  - ESLint"
    Write-Host "  - Test Explorer UI"
    Write-Host "  - GitLens"
}

# Setup Git configuration
Write-Host ""
Write-Host "📝 Configuring Git..." -ForegroundColor Blue

# Check if Git user is configured
$gitUserName = git config --global user.name
if (-not $gitUserName) {
    git config --global user.name "jeff9497"
    Write-Success "Git user.name configured"
} else {
    Write-Success "Git user.name already configured: $gitUserName"
}

$gitUserEmail = git config --global user.email
if (-not $gitUserEmail) {
    git config --global user.email "<EMAIL>"
    Write-Success "Git user.email configured"
} else {
    Write-Success "Git user.email already configured: $gitUserEmail"
}

# Create .env template if it doesn't exist
Write-Host ""
Write-Host "⚙️  Setting up environment..." -ForegroundColor Blue

if (-not (Test-Path ".env")) {
    $envContent = @"
# 200Model8 Extension - Environment Variables
# Copy this file and add your actual API keys

# Puter.js API Key
PUTER_API_KEY=your_puter_api_key_here

# OpenRouter API Key  
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Development settings
NODE_ENV=development
DEBUG=true
"@
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    Write-Success "Created .env template file"
    Write-Info "Please edit .env file and add your actual API keys"
} else {
    Write-Success ".env file already exists"
}

# Final setup verification
Write-Host ""
Write-Host "🧪 Running setup verification..." -ForegroundColor Blue

# Test compilation
npm run compile > $null 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Success "Compilation test passed"
} else {
    Write-Error "Compilation test failed"
}

# Test linting
npm run lint > $null 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Success "Linting test passed"
} else {
    Write-Warning "Linting test found issues"
}

Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Blue
Write-Host "1. Open VS Code: " -NoNewline; Write-Host "code ." -ForegroundColor Yellow
Write-Host "2. Edit .env file with your API keys"
Write-Host "3. Press " -NoNewline; Write-Host "F5" -ForegroundColor Yellow -NoNewline; Write-Host " to launch Extension Development Host"
Write-Host "4. Test your extension in the new VS Code window"
Write-Host ""
Write-Host "🔗 Useful Commands:" -ForegroundColor Blue
Write-Host "• " -NoNewline; Write-Host "npm run dev" -ForegroundColor Yellow -NoNewline; Write-Host "        - Start development mode"
Write-Host "• " -NoNewline; Write-Host "npm run watch" -ForegroundColor Yellow -NoNewline; Write-Host "      - Watch for changes"
Write-Host "• " -NoNewline; Write-Host "npm test" -ForegroundColor Yellow -NoNewline; Write-Host "           - Run tests"
Write-Host "• " -NoNewline; Write-Host "npm run package" -ForegroundColor Yellow -NoNewline; Write-Host "    - Create .vsix package"
Write-Host ""
Write-Host "📚 Documentation:" -ForegroundColor Blue
Write-Host "• README.md         - Main documentation"
Write-Host "• DEVELOPMENT.md    - Development guide"
Write-Host "• BUILD.md          - Build and deployment guide"
Write-Host ""
Write-Host "Happy coding! 🚀" -ForegroundColor Green
