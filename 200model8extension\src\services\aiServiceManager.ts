import * as vscode from 'vscode';
import { PuterService } from './puterService';
import { OpenRouterService } from './openRouterService';
import { 
  IPuterService, 
  CodeContext, 
  ProjectInfo, 
  ProjectStructure, 
  DebuggingHelp, 
  OptimizedCode 
} from '../types/extension';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';
import { handleConfigurationError } from '../utils/errorHandler';

export type AIProvider = 'puter' | 'openrouter';

export interface ModelInfo {
  id: string;
  name: string;
  provider: AIProvider;
  description?: string;
  isFree?: boolean;
  contextLength?: number;
  pricing?: {
    prompt: string;
    completion: string;
  };
}

export class AIServiceManager implements IPuterService {
  private static instance: AIServiceManager;
  private puterService: PuterService;
  private openRouterService: OpenRouterService;
  private currentProvider: AIProvider;
  private availableModels: ModelInfo[] = [];

  private constructor() {
    this.puterService = PuterService.getInstance();
    this.openRouterService = OpenRouterService.getInstance();
    this.currentProvider = configManager.getConfig().aiProvider as AIProvider || 'puter';
  }

  public static getInstance(): AIServiceManager {
    if (!AIServiceManager.instance) {
      AIServiceManager.instance = new AIServiceManager();
    }
    return AIServiceManager.instance;
  }

  public async initializeConnection(): Promise<void> {
    try {
      const config = configManager.getConfig();
      this.currentProvider = config.aiProvider as AIProvider || 'puter';

      logger.info(`Initializing AI service with provider: ${this.currentProvider}`);

      // Initialize the current provider
      await this.getCurrentService().initializeConnection();

      // Load available models from both providers
      await this.loadAllAvailableModels();

      logger.info('AI service manager initialized successfully');
    } catch (error) {
      handleConfigurationError(error as Error, 'AI service initialization');
      throw error;
    }
  }

  public async switchProvider(provider: AIProvider): Promise<void> {
    try {
      if (provider === this.currentProvider) {
        return;
      }

      logger.info(`Switching AI provider from ${this.currentProvider} to ${provider}`);

      this.currentProvider = provider;
      await configManager.updateConfig('aiProvider', provider);

      // Initialize the new provider
      await this.getCurrentService().initializeConnection();

      // Reload models
      await this.loadAllAvailableModels();

      vscode.window.showInformationMessage(`Switched to ${provider === 'puter' ? 'Puter.js' : 'OpenRouter'}`);
      
    } catch (error) {
      handleConfigurationError(error as Error, `Switching to ${provider}`);
      throw error;
    }
  }

  public async loadAllAvailableModels(): Promise<void> {
    try {
      this.availableModels = [];

      // Load Puter models
      try {
        const puterModels = await this.puterService.getAvailableModels();
        this.availableModels.push(...puterModels.map(id => ({
          id,
          name: this.formatModelName(id),
          provider: 'puter' as AIProvider,
          description: `Puter.js model: ${id}`
        })));
      } catch (error) {
        logger.warn('Failed to load Puter models:', error as Error);
      }

      // Load OpenRouter models
      try {
        const openRouterModels = await this.openRouterService.getAvailableModels();
        const freeModels = await this.openRouterService.getFreeModels();
        const freeModelIds = new Set(freeModels.map(m => m.id));

        for (const modelId of openRouterModels) {
          const modelDetails = await this.openRouterService.getModelDetails(modelId);
          this.availableModels.push({
            id: modelId,
            name: modelDetails?.name || this.formatModelName(modelId),
            provider: 'openrouter' as AIProvider,
            description: modelDetails?.description,
            isFree: freeModelIds.has(modelId),
            contextLength: modelDetails?.context_length,
            pricing: modelDetails?.pricing
          });
        }
      } catch (error) {
        logger.warn('Failed to load OpenRouter models:', error as Error);
      }

      logger.info(`Loaded ${this.availableModels.length} total models`);
    } catch (error) {
      logger.error('Failed to load available models:', error as Error);
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    if (this.availableModels.length === 0) {
      await this.loadAllAvailableModels();
    }
    return this.availableModels.map(model => model.id);
  }

  public getModelsByProvider(provider: AIProvider): ModelInfo[] {
    return this.availableModels.filter(model => model.provider === provider);
  }

  public getFreeModels(): ModelInfo[] {
    return this.availableModels.filter(model => model.isFree === true);
  }

  public getAllModelsInfo(): ModelInfo[] {
    return [...this.availableModels];
  }

  public async selectModel(): Promise<string | undefined> {
    const modelOptions = this.availableModels.map(model => ({
      label: model.name,
      description: `${model.provider.toUpperCase()}${model.isFree ? ' (Free)' : ''} - ${model.description || ''}`,
      detail: model.pricing ? `Prompt: $${model.pricing.prompt}/1K, Completion: $${model.pricing.completion}/1K` : '',
      value: model.id,
      provider: model.provider
    }));

    const selected = await vscode.window.showQuickPick(modelOptions, {
      placeHolder: 'Select AI model',
      ignoreFocusOut: true,
      matchOnDescription: true,
      matchOnDetail: true
    });

    if (selected) {
      // Switch provider if necessary
      if (selected.provider !== this.currentProvider) {
        await this.switchProvider(selected.provider);
      }

      await this.switchModel(selected.value);
      return selected.value;
    }

    return undefined;
  }

  public async switchModel(modelName: string): Promise<void> {
    try {
      const model = this.availableModels.find(m => m.id === modelName);
      if (!model) {
        throw new Error(`Model ${modelName} not found`);
      }

      // Switch provider if necessary
      if (model.provider !== this.currentProvider) {
        await this.switchProvider(model.provider);
      }

      // Switch to the model
      await this.getCurrentService().switchModel(modelName);
      
      logger.info(`Switched to model: ${modelName} (${model.provider})`);
    } catch (error) {
      handleConfigurationError(error as Error, `Switching to model ${modelName}`);
      throw error;
    }
  }

  // Delegate methods to current service
  public async generateCode(prompt: string, context?: CodeContext): Promise<string> {
    return this.getCurrentService().generateCode(prompt, context);
  }

  public async explainCode(code: string, language: string): Promise<string> {
    return this.getCurrentService().explainCode(code, language);
  }

  public async refactorCode(code: string, instructions: string): Promise<string> {
    return this.getCurrentService().refactorCode(code, instructions);
  }

  public async generateTests(code: string, language: string): Promise<string> {
    return this.getCurrentService().generateTests(code, language);
  }

  public async streamResponse(prompt: string, onChunk: (chunk: string) => void): Promise<void> {
    return this.getCurrentService().streamResponse(prompt, onChunk);
  }

  public async setProjectContext(projectInfo: ProjectInfo): Promise<void> {
    return this.getCurrentService().setProjectContext(projectInfo);
  }

  public async addFileContext(filePath: string, content: string): Promise<void> {
    return this.getCurrentService().addFileContext(filePath, content);
  }

  public async clearContext(): Promise<void> {
    return this.getCurrentService().clearContext();
  }

  public async generateProject(description: string, requirements: string[]): Promise<ProjectStructure> {
    return this.getCurrentService().generateProject(description, requirements);
  }

  public async debugCode(code: string, errorMessage: string): Promise<DebuggingHelp> {
    return this.getCurrentService().debugCode(code, errorMessage);
  }

  public async optimizeCode(code: string, language: string): Promise<OptimizedCode> {
    return this.getCurrentService().optimizeCode(code, language);
  }

  // Utility methods
  public getCurrentProvider(): AIProvider {
    return this.currentProvider;
  }

  public getCurrentService(): IPuterService {
    return this.currentProvider === 'puter' ? this.puterService : this.openRouterService;
  }

  private formatModelName(modelId: string): string {
    // Convert model IDs to readable names
    const nameMap: { [key: string]: string } = {
      'gpt-4': 'GPT-4',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'claude-3-opus': 'Claude 3 Opus',
      'claude-3-sonnet': 'Claude 3 Sonnet',
      'gemini-pro': 'Gemini Pro',
      'openai/gpt-4': 'GPT-4 (OpenRouter)',
      'openai/gpt-3.5-turbo': 'GPT-3.5 Turbo (OpenRouter)',
      'anthropic/claude-3-opus': 'Claude 3 Opus (OpenRouter)',
      'anthropic/claude-3-sonnet': 'Claude 3 Sonnet (OpenRouter)',
      'google/gemini-pro': 'Gemini Pro (OpenRouter)'
    };

    return nameMap[modelId] || modelId.split('/').pop() || modelId;
  }

  public async showProviderStatus(): Promise<void> {
    const config = configManager.getConfig();
    const puterConfigured = !!config.puterApiKey;
    const openRouterConfigured = !!config.openRouterApiKey;

    const statusMessage = `**AI Provider Status**

**Current Provider:** ${this.currentProvider === 'puter' ? 'Puter.js' : 'OpenRouter'}

**Puter.js:** ${puterConfigured ? '✅ Configured' : '❌ Not configured'}
**OpenRouter:** ${openRouterConfigured ? '✅ Configured' : '❌ Not configured'}

**Available Models:** ${this.availableModels.length}
- Puter models: ${this.getModelsByProvider('puter').length}
- OpenRouter models: ${this.getModelsByProvider('openrouter').length}
- Free models: ${this.getFreeModels().length}

**Configuration:**
- Show free models only: ${config.showFreeModelsOnly ? 'Yes' : 'No'}
- Max tokens: ${config.maxTokens}
- Context lines: ${config.contextLines}`;

    const doc = await vscode.workspace.openTextDocument({
      content: statusMessage,
      language: 'markdown'
    });

    await vscode.window.showTextDocument(doc);
  }
}
