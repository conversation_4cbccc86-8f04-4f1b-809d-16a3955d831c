import * as vscode from 'vscode';
import { CodeService } from '../services/codeService';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';

export class CompletionProvider implements vscode.CompletionItemProvider {
  private codeService: CodeService;

  constructor() {
    this.codeService = CodeService.getInstance();
  }

  public async provideCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    token: vscode.CancellationToken,
    context: vscode.CompletionContext
  ): Promise<vscode.CompletionItem[]> {
    try {
      // Check if AI completions are enabled
      const config = configManager.getConfig();
      if (!config.puterApiKey) {
        return [];
      }

      // Get current line and context
      const line = document.lineAt(position);
      const lineText = line.text.substring(0, position.character);
      
      // Skip if the line is empty or just whitespace
      if (!lineText.trim()) {
        return [];
      }

      // Skip if we're in a comment
      if (this.isInComment(document, position)) {
        return [];
      }

      // Get surrounding context
      const contextLines = Math.min(config.contextLines, 50); // Limit for performance
      const startLine = Math.max(0, position.line - contextLines);
      const endLine = Math.min(document.lineCount - 1, position.line + contextLines);
      
      const contextRange = new vscode.Range(startLine, 0, endLine, 0);
      const contextText = document.getText(contextRange);

      // Get AI completions
      const completions = await this.codeService.provideCompletions(
        contextText,
        position.line - startLine,
        document.languageId
      );

      // Convert to VS Code completion items
      const vscodeCompletions = completions.map(completion => {
        const item = new vscode.CompletionItem(
          completion.label,
          this.getCompletionKind(completion.kind)
        );
        
        item.detail = completion.detail || 'AI Generated';
        item.documentation = new vscode.MarkdownString(completion.documentation || '');
        item.insertText = completion.insertText;
        item.sortText = `z${completion.confidence}${completion.label}`; // Sort AI completions last
        
        // Add AI indicator
        item.detail = `🤖 ${item.detail}`;
        
        return item;
      });

      logger.debug(`Provided ${vscodeCompletions.length} AI completions`);
      return vscodeCompletions;

    } catch (error) {
      logger.error('Failed to provide completions:', error as Error);
      return [];
    }
  }

  public resolveCompletionItem(
    item: vscode.CompletionItem,
    token: vscode.CancellationToken
  ): vscode.ProviderResult<vscode.CompletionItem> {
    // Add additional documentation or examples if needed
    return item;
  }

  private isInComment(document: vscode.TextDocument, position: vscode.Position): boolean {
    const line = document.lineAt(position);
    const lineText = line.text;
    const language = document.languageId;

    // Check for single-line comments
    const singleLineComments = {
      'javascript': '//',
      'typescript': '//',
      'python': '#',
      'java': '//',
      'csharp': '//',
      'cpp': '//',
      'c': '//',
      'go': '//',
      'rust': '//',
      'php': '//',
      'ruby': '#'
    };

    const commentStart = singleLineComments[language as keyof typeof singleLineComments];
    if (commentStart) {
      const commentIndex = lineText.indexOf(commentStart);
      if (commentIndex !== -1 && commentIndex < position.character) {
        return true;
      }
    }

    // TODO: Add multi-line comment detection
    return false;
  }

  private getCompletionKind(kind: vscode.CompletionItemKind): vscode.CompletionItemKind {
    // Map our completion kinds to VS Code kinds
    switch (kind) {
      case vscode.CompletionItemKind.Function:
        return vscode.CompletionItemKind.Function;
      case vscode.CompletionItemKind.Variable:
        return vscode.CompletionItemKind.Variable;
      case vscode.CompletionItemKind.Class:
        return vscode.CompletionItemKind.Class;
      case vscode.CompletionItemKind.Method:
        return vscode.CompletionItemKind.Method;
      case vscode.CompletionItemKind.Property:
        return vscode.CompletionItemKind.Property;
      case vscode.CompletionItemKind.Keyword:
        return vscode.CompletionItemKind.Keyword;
      default:
        return vscode.CompletionItemKind.Text;
    }
  }
}
