import * as vscode from 'vscode';
import { ChatView } from '../views/chatView';

export class <PERSON>tProvider implements vscode.TreeDataProvider<ChatItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<ChatItem | undefined | null | void> = new vscode.EventEmitter<ChatItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<ChatItem | undefined | null | void> = this._onDidChangeTreeData.event;

  constructor() {}

  refresh(): void {
    this._onDidChangeTreeData.fire();
  }

  getTreeItem(element: ChatItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: ChatItem): Thenable<ChatItem[]> {
    if (!element) {
      // Root level items
      return Promise.resolve([
        new ChatItem('Open AI Chat', 'Open the AI chat interface', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.openChat',
          title: 'Open Chat'
        }),
        new ChatItem('Quick Actions', 'Quick AI actions', vscode.TreeItemCollapsibleState.Collapsed),
        new ChatItem('Recent Conversations', 'Recent chat history', vscode.TreeItemCollapsibleState.Collapsed),
        new ChatItem('Settings', 'Extension settings', vscode.TreeItemCollapsibleState.Collapsed)
      ]);
    } else if (element.label === 'Quick Actions') {
      return Promise.resolve([
        new ChatItem('Generate Code', 'Generate code with AI', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.generateCode',
          title: 'Generate Code'
        }),
        new ChatItem('Explain Code', 'Explain selected code', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.explainCode',
          title: 'Explain Code'
        }),
        new ChatItem('Refactor Code', 'Refactor selected code', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.refactorCode',
          title: 'Refactor Code'
        }),
        new ChatItem('Generate Tests', 'Generate unit tests', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.generateTests',
          title: 'Generate Tests'
        }),
        new ChatItem('Create File', 'Create new file with AI', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.createFile',
          title: 'Create File'
        }),
        new ChatItem('Generate Project', 'Generate project structure', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.generateProject',
          title: 'Generate Project'
        })
      ]);
    } else if (element.label === 'Recent Conversations') {
      // TODO: Implement conversation history
      return Promise.resolve([
        new ChatItem('No recent conversations', 'Start a new chat to see history', vscode.TreeItemCollapsibleState.None)
      ]);
    } else if (element.label === 'Settings') {
      return Promise.resolve([
        new ChatItem('Switch AI Model', 'Change the AI model', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.switchModel',
          title: 'Switch Model'
        }),
        new ChatItem('Clear Context', 'Clear AI conversation context', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.clearContext',
          title: 'Clear Context'
        }),
        new ChatItem('Show Logs', 'Show extension logs', vscode.TreeItemCollapsibleState.None, {
          command: '200model8.showLogs',
          title: 'Show Logs'
        }),
        new ChatItem('Open Settings', 'Open extension settings', vscode.TreeItemCollapsibleState.None, {
          command: 'workbench.action.openSettings',
          title: 'Open Settings',
          arguments: ['200model8']
        })
      ]);
    }

    return Promise.resolve([]);
  }
}

class ChatItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly tooltip: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly command?: vscode.Command
  ) {
    super(label, collapsibleState);
    this.tooltip = tooltip;
    this.command = command;

    // Set icons based on label
    this.iconPath = this.getIcon();
  }

  private getIcon(): vscode.ThemeIcon {
    switch (this.label) {
      case 'Open AI Chat':
        return new vscode.ThemeIcon('comment-discussion');
      case 'Quick Actions':
        return new vscode.ThemeIcon('zap');
      case 'Generate Code':
        return new vscode.ThemeIcon('code');
      case 'Explain Code':
        return new vscode.ThemeIcon('question');
      case 'Refactor Code':
        return new vscode.ThemeIcon('wrench');
      case 'Generate Tests':
        return new vscode.ThemeIcon('beaker');
      case 'Create File':
        return new vscode.ThemeIcon('new-file');
      case 'Generate Project':
        return new vscode.ThemeIcon('folder');
      case 'Recent Conversations':
        return new vscode.ThemeIcon('history');
      case 'Settings':
        return new vscode.ThemeIcon('settings-gear');
      case 'Switch AI Model':
        return new vscode.ThemeIcon('arrow-swap');
      case 'Clear Context':
        return new vscode.ThemeIcon('clear-all');
      case 'Show Logs':
        return new vscode.ThemeIcon('output');
      case 'Open Settings':
        return new vscode.ThemeIcon('settings');
      default:
        return new vscode.ThemeIcon('circle-outline');
    }
  }
}
