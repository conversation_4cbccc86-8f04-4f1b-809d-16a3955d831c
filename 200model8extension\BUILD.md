# 200Model8 Extension - Build and Deployment Guide

This guide covers building, testing, and deploying the 200Model8 VS Code extension.

## Prerequisites

### Required Software
- **Node.js** (v16 or higher)
- **npm** (v8 or higher)
- **VS Code** (v1.74.0 or higher)
- **TypeScript** (v4.9 or higher)
- **Git** (for version control)

### Development Tools
```bash
# Install VS Code Extension Manager
npm install -g vsce

# Install TypeScript globally (optional)
npm install -g typescript

# Install ESLint globally (optional)
npm install -g eslint
```

## Project Setup

### 1. Clone and Install Dependencies
```bash
# Clone the repository
git clone https://github.com/jeff9497/200model8extension.git
cd 200model8extension

# Install dependencies
npm install

# Install development dependencies
npm install --save-dev
```

### 2. Environment Configuration
Create a `.env` file for development (not included in repository):
```env
# Development API keys (optional for testing)
PUTER_API_KEY=your_puter_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

## Development Workflow

### 1. Development Mode
```bash
# Start TypeScript compiler in watch mode
npm run dev

# Or manually
npm run watch
```

### 2. Testing the Extension
```bash
# Open VS Code with the extension
code .

# Press F5 to launch Extension Development Host
# This opens a new VS Code window with your extension loaded
```

### 3. Running Tests
```bash
# Run all tests
npm test

# Run linting
npm run lint

# Run compilation check
npm run compile
```

## Building for Production

### 1. Clean Build
```bash
# Clean previous builds
npm run clean

# Full build
npm run build
```

### 2. Package Creation
```bash
# Create .vsix package
npm run package

# Or manually
vsce package
```

This creates a `.vsix` file that can be installed in VS Code.

### 3. Local Installation
```bash
# Install the packaged extension locally
code --install-extension 200model8-extension-1.0.0.vsix
```

## Publishing to VS Code Marketplace

### 1. Prerequisites for Publishing
- **Azure DevOps Account** (for Personal Access Token)
- **VS Code Marketplace Publisher Account**
- **Verified Publisher Profile**

### 2. Setup Publisher Account
```bash
# Login to vsce
vsce login your-publisher-name

# Create publisher (first time only)
vsce create-publisher your-publisher-name
```

### 3. Publishing Process
```bash
# Publish to marketplace
npm run publish

# Or manually with version bump
vsce publish patch  # 1.0.0 -> 1.0.1
vsce publish minor  # 1.0.0 -> 1.1.0
vsce publish major  # 1.0.0 -> 2.0.0
```

## Configuration and API Keys

### 1. Puter.js Setup
1. Visit [Puter.com](https://puter.com)
2. Create an account
3. Generate API key
4. Add to VS Code settings: `200model8.puterApiKey`

### 2. OpenRouter Setup
1. Visit [OpenRouter.ai](https://openrouter.ai)
2. Create an account
3. Generate API key
4. Add to VS Code settings: `200model8.openRouterApiKey`

### 3. Extension Configuration
```json
{
  "200model8.aiProvider": "puter",
  "200model8.puterApiKey": "your-puter-key",
  "200model8.openRouterApiKey": "your-openrouter-key",
  "200model8.defaultModel": "gpt-4",
  "200model8.autoSave": true,
  "200model8.contextLines": 100,
  "200model8.maxTokens": 4000,
  "200model8.showFreeModelsOnly": false
}
```

## Quality Assurance

### 1. Pre-Release Checklist
- [ ] All tests pass (`npm test`)
- [ ] No linting errors (`npm run lint`)
- [ ] Extension loads without errors
- [ ] All commands work correctly
- [ ] Chat interface functions properly
- [ ] File operations work as expected
- [ ] Both AI providers work (if configured)
- [ ] Documentation is up to date

### 2. Testing Scenarios
1. **Basic Functionality**
   - Extension activation
   - Command palette integration
   - Settings configuration

2. **AI Integration**
   - Provider switching
   - Model selection
   - Code generation
   - Chat functionality

3. **File Operations**
   - File creation
   - File editing
   - Project generation
   - Search and replace

4. **Error Handling**
   - Invalid API keys
   - Network failures
   - File permission errors
   - Malformed requests

### 3. Performance Testing
```bash
# Monitor extension performance
# Check VS Code Developer Tools (Help > Toggle Developer Tools)
# Monitor memory usage and response times
```

## Troubleshooting

### Common Build Issues

#### TypeScript Compilation Errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Fix import/export issues
npm run lint --fix
```

#### Missing Dependencies
```bash
# Reinstall all dependencies
rm -rf node_modules package-lock.json
npm install
```

#### VSCE Package Errors
```bash
# Update vsce
npm install -g vsce@latest

# Check package.json configuration
vsce ls
```

### Runtime Issues

#### Extension Not Loading
1. Check VS Code version compatibility
2. Verify package.json activation events
3. Check extension host logs
4. Restart VS Code

#### API Connection Issues
1. Verify API keys are correct
2. Check network connectivity
3. Review rate limiting
4. Check service status

#### File Operation Failures
1. Check file permissions
2. Verify workspace access
3. Check available disk space
4. Review file paths

## Deployment Strategies

### 1. Development Deployment
- Local installation via `.vsix`
- Extension Development Host
- Side-loading for testing

### 2. Beta Deployment
- Pre-release versions
- Limited user testing
- Feedback collection

### 3. Production Deployment
- VS Code Marketplace
- Automatic updates
- Version management

## Monitoring and Analytics

### 1. Extension Telemetry
- Usage statistics
- Error reporting
- Performance metrics
- Feature adoption

### 2. User Feedback
- GitHub issues
- Marketplace reviews
- Community forums
- Direct support

## Security Considerations

### 1. API Key Security
- Never commit API keys to repository
- Use VS Code secure storage
- Implement key validation
- Provide clear security guidelines

### 2. Code Security
- Input validation
- Output sanitization
- Secure API communications
- Regular dependency updates

## Maintenance

### 1. Regular Updates
- Security patches
- Dependency updates
- VS Code API compatibility
- New feature additions

### 2. Version Management
- Semantic versioning
- Changelog maintenance
- Migration guides
- Backward compatibility

## Support and Documentation

### 1. User Documentation
- README.md
- CHANGELOG.md
- Configuration guides
- Troubleshooting guides

### 2. Developer Documentation
- Code comments
- API documentation
- Architecture overview
- Contributing guidelines

---

## Quick Commands Reference

```bash
# Development
npm run dev          # Start development mode
npm run watch        # Watch for changes
npm test            # Run tests
npm run lint        # Check code quality

# Building
npm run clean       # Clean build artifacts
npm run compile     # Compile TypeScript
npm run build       # Full build process

# Packaging
npm run package     # Create .vsix package
npm run publish     # Publish to marketplace

# Utilities
vsce ls            # List package contents
vsce show          # Show package info
code --list-extensions  # List installed extensions
```

For more detailed information, refer to the [VS Code Extension API documentation](https://code.visualstudio.com/api) and the [vsce publishing guide](https://code.visualstudio.com/api/working-with-extensions/publishing-extension).
