# 200Model8 Extension - Development Setup Guide

## 🚀 Quick Start

### 1. Prerequisites Installation

**Install Node.js and npm:**
```bash
# Download from https://nodejs.org/ (LTS version recommended)
# Or use package managers:

# Windows (using Chocolatey)
choco install nodejs

# macOS (using Homebrew)
brew install node

# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

**Install VS Code:**
- Download from: https://code.visualstudio.com/
- Or use package managers as above

**Install Git:**
- Download from: https://git-scm.com/
- Or use package managers as above

### 2. Clone and Setup Repository

```bash
# Clone the repository
git clone https://github.com/jeff9497/200Model8Extension.git
cd 200Model8Extension

# Install dependencies
npm install

# Install global tools
npm install -g vsce typescript eslint
```

### 3. Development Environment Setup

**Install VS Code Extensions (Recommended):**
```bash
# Install via command line
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension dbaeumer.vscode-eslint
code --install-extension ms-vscode.test-adapter-converter
code --install-extension hbenl.vscode-test-explorer
```

**Or install via VS Code Extensions panel:**
- TypeScript and JavaScript Language Features
- ESLint
- Test Explorer UI
- GitLens (optional but helpful)

## 🛠 Development Workflow

### 1. Start Development Mode

```bash
# Start TypeScript compiler in watch mode
npm run dev

# Or manually
npm run watch
```

### 2. Launch Extension Development Host

**Method 1: VS Code Command Palette**
1. Open the project in VS Code: `code .`
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Developer: Reload Window" and select it
4. Press `F5` to launch Extension Development Host

**Method 2: Debug Panel**
1. Open VS Code
2. Go to Run and Debug panel (`Ctrl+Shift+D`)
3. Select "Run Extension" from dropdown
4. Click the green play button or press `F5`

**Method 3: Launch Configuration**
The project includes `.vscode/launch.json` with pre-configured debug settings.

### 3. Testing Your Extension

When you press `F5`, a new VS Code window opens called **"Extension Development Host"**. This is where you test your extension:

1. **Extension Development Host** window opens
2. Your extension is automatically loaded
3. Open Command Palette (`Ctrl+Shift+P`)
4. Type "200Model8" to see available commands
5. Test features like:
   - `200Model8: Open AI Chat`
   - `200Model8: Generate Code`
   - `200Model8: Create File`

### 4. Making Changes

1. Edit code in the main VS Code window
2. Save changes (`Ctrl+S`)
3. In Extension Development Host, reload window:
   - `Ctrl+Shift+P` → "Developer: Reload Window"
   - Or press `Ctrl+R`

## 📁 Project Structure

```
200Model8Extension/
├── .vscode/                 # VS Code configuration
│   ├── launch.json         # Debug configuration
│   ├── settings.json       # Workspace settings
│   └── tasks.json          # Build tasks
├── .github/                # GitHub Actions
│   └── workflows/
│       └── ci.yml          # CI/CD pipeline
├── src/                    # Source code
│   ├── commands/           # Command implementations
│   ├── providers/          # VS Code providers
│   ├── services/           # Core services
│   ├── types/              # TypeScript definitions
│   ├── utils/              # Utility functions
│   ├── views/              # UI components
│   ├── webview/            # Chat interface
│   └── extension.ts        # Main entry point
├── out/                    # Compiled JavaScript (auto-generated)
├── package.json            # Extension manifest
├── tsconfig.json           # TypeScript configuration
├── .eslintrc.json          # ESLint configuration
└── README.md               # Documentation
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run specific test file
npm test -- --grep "specific test name"

# Run tests in watch mode
npm run test:watch
```

### Manual Testing Checklist

**Basic Functionality:**
- [ ] Extension activates without errors
- [ ] Commands appear in Command Palette
- [ ] Settings are accessible
- [ ] Chat interface opens

**AI Integration:**
- [ ] Provider switching works
- [ ] Model selection functions
- [ ] Code generation works
- [ ] Chat responses appear

**File Operations:**
- [ ] File creation works
- [ ] File editing functions
- [ ] Project generation works
- [ ] Search and replace works

## 🔧 Configuration

### API Keys Setup
1. Open VS Code Settings (`Ctrl+,`)
2. Search for "200model8"
3. Configure:
   - `200model8.puterApiKey`: Your Puter.js API key
   - `200model8.openRouterApiKey`: Your OpenRouter API key
   - `200model8.aiProvider`: Choose "puter" or "openrouter"

### Development Settings
Create `.vscode/settings.json` in your workspace:
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "eslint.validate": ["typescript"],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 🐛 Debugging

### Debug Extension Code
1. Set breakpoints in your TypeScript code
2. Press `F5` to launch Extension Development Host
3. Trigger the code path with breakpoints
4. Debug in the main VS Code window

### Debug Webview (Chat Interface)
1. In Extension Development Host, open chat
2. Right-click in chat interface
3. Select "Inspect Element"
4. Use browser dev tools to debug HTML/CSS/JS

### Common Issues

**Extension not loading:**
- Check VS Code version compatibility
- Verify package.json activation events
- Check Output panel for errors

**TypeScript errors:**
- Run `npm run compile` to check compilation
- Verify all imports are correct
- Check tsconfig.json configuration

**Tests failing:**
- Ensure all dependencies are installed
- Check test environment setup
- Review test configuration

## 📦 Building and Packaging

### Development Build
```bash
npm run compile
```

### Production Build
```bash
npm run build
```

### Create Package
```bash
npm run package
# Creates .vsix file for distribution
```

### Install Locally
```bash
code --install-extension 200model8-extension-1.0.0.vsix
```

## 🚀 Publishing

### Prepare for Publishing
1. Update version in `package.json`
2. Update `CHANGELOG.md`
3. Test thoroughly
4. Create release notes

### Publish to Marketplace
```bash
# Login to vsce (first time only)
vsce login your-publisher-name

# Publish
npm run publish
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📞 Support

- **Issues**: https://github.com/jeff9497/200Model8Extension/issues
- **Discussions**: https://github.com/jeff9497/200Model8Extension/discussions
- **Email**: <EMAIL>

---

**Happy coding with AI assistance! 🎯**
