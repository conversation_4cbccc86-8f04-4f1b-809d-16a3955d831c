import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as vscode from 'vscode';
import { 
  IPuterService, 
  CodeContext, 
  ProjectInfo, 
  ProjectStructure, 
  DebuggingHelp, 
  OptimizedCode 
} from '../types/extension';
import {
  PuterServiceConfig,
  PuterContextMessage,
  PuterCodeRequest,
  PuterCodeResponse,
  PuterProjectRequest,
  PuterProjectResponse
} from '../types/puter';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';

export class PuterService implements IPuterService {
  private static instance: PuterService;
  private client: AxiosInstance;
  private config: PuterServiceConfig;
  private contextMessages: PuterContextMessage[] = [];
  private currentModel: string;

  private constructor() {
    const extensionConfig = configManager.getConfig();
    
    this.config = {
      apiKey: extensionConfig.puterApiKey,
      defaultModel: extensionConfig.defaultModel,
      maxTokens: extensionConfig.maxTokens,
      temperature: 0.7,
      timeout: 30000,
      retries: 3
    };

    this.currentModel = this.config.defaultModel;
    this.initializeClient();
  }

  public static getInstance(): PuterService {
    if (!PuterService.instance) {
      PuterService.instance = new PuterService();
    }
    return PuterService.instance;
  }

  private initializeClient(): void {
    this.client = axios.create({
      baseURL: 'https://api.puter.com/v1',
      timeout: this.config.timeout,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': '200Model8Extension/1.0.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Puter API Request:', {
          url: config.url,
          method: config.method,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('Puter API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Puter API Response:', {
          status: response.status,
          data: response.data
        });
        return response;
      },
      async (error) => {
        logger.error('Puter API Response Error:', error);
        
        if (error.response?.status === 401) {
          vscode.window.showErrorMessage('Invalid Puter API key. Please check your configuration.');
          await configManager.promptForApiKey();
        } else if (error.response?.status === 429) {
          vscode.window.showWarningMessage('Rate limit exceeded. Please try again later.');
        } else if (error.response?.status >= 500) {
          vscode.window.showErrorMessage('Puter service is temporarily unavailable.');
        }
        
        return Promise.reject(error);
      }
    );
  }

  public async initializeConnection(): Promise<void> {
    try {
      logger.info('Initializing Puter connection...');
      
      if (!this.config.apiKey) {
        const apiKey = await configManager.promptForApiKey();
        if (!apiKey) {
          throw new Error('API key is required');
        }
        this.config.apiKey = apiKey;
        this.initializeClient();
      }

      // Test connection
      await this.getAvailableModels();
      logger.info('Puter connection initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize Puter connection:', error as Error);
      throw error;
    }
  }

  public async switchModel(modelName: string): Promise<void> {
    try {
      const availableModels = await this.getAvailableModels();
      if (!availableModels.includes(modelName)) {
        throw new Error(`Model ${modelName} is not available`);
      }
      
      this.currentModel = modelName;
      await configManager.updateConfig('defaultModel', modelName);
      logger.info(`Switched to model: ${modelName}`);
      
    } catch (error) {
      logger.error(`Failed to switch to model ${modelName}:`, error as Error);
      throw error;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      const models = response.data.data || [];
      return models.map((model: any) => model.id);
    } catch (error) {
      logger.error('Failed to get available models:', error as Error);
      // Return default models if API call fails
      return ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'gemini-pro'];
    }
  }

  public async generateCode(prompt: string, context?: CodeContext): Promise<string> {
    try {
      const messages = this.buildMessages(prompt, context);
      
      const request = {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      };

      const response = await this.client.post('/chat/completions', request);
      const content = response.data.choices[0]?.message?.content || '';
      
      logger.info('Code generated successfully', { prompt: prompt.substring(0, 100) });
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      logger.error('Failed to generate code:', error as Error);
      throw new Error(`Code generation failed: ${(error as any).message}`);
    }
  }

  public async explainCode(code: string, language: string): Promise<string> {
    try {
      const prompt = `Please explain the following ${language} code in detail:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nProvide a clear explanation of what this code does, how it works, and any important details.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert code reviewer and teacher. Provide clear, detailed explanations of code.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.3
      });

      return response.data.choices[0]?.message?.content || 'Unable to explain code.';
      
    } catch (error) {
      logger.error('Failed to explain code:', error as Error);
      throw new Error(`Code explanation failed: ${(error as any).message}`);
    }
  }

  public async refactorCode(code: string, instructions: string): Promise<string> {
    try {
      const prompt = `Please refactor the following code according to these instructions: ${instructions}\n\nOriginal code:\n\`\`\`\n${code}\n\`\`\`\n\nProvide only the refactored code without explanations.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert software engineer. Refactor code according to the given instructions while maintaining functionality.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.2
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      logger.error('Failed to refactor code:', error as Error);
      throw new Error(`Code refactoring failed: ${(error as any).message}`);
    }
  }

  public async generateTests(code: string, language: string): Promise<string> {
    try {
      const prompt = `Generate comprehensive unit tests for the following ${language} code:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nInclude test cases for normal operation, edge cases, and error conditions. Use appropriate testing framework for ${language}.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert in test-driven development. Generate comprehensive, well-structured unit tests.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.3
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      logger.error('Failed to generate tests:', error as Error);
      throw new Error(`Test generation failed: ${(error as any).message}`);
    }
  }

  public async streamResponse(prompt: string, onChunk: (chunk: string) => void): Promise<void> {
    try {
      const messages = this.buildMessages(prompt);
      
      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      }, {
        responseType: 'stream'
      });

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                onChunk(content);
              }
            } catch (e) {
              // Ignore parsing errors for streaming
            }
          }
        }
      });
      
    } catch (error) {
      logger.error('Failed to stream response:', error as Error);
      throw error;
    }
  }

  public async setProjectContext(projectInfo: ProjectInfo): Promise<void> {
    const contextMessage: PuterContextMessage = {
      role: 'system',
      content: `Project Context: ${projectInfo.name} (${projectInfo.language}${projectInfo.framework ? `, ${projectInfo.framework}` : ''}). Dependencies: ${projectInfo.dependencies.join(', ')}`,
      metadata: {
        type: 'instruction',
        timestamp: new Date()
      }
    };
    
    this.contextMessages.unshift(contextMessage);
    this.contextMessages = this.contextMessages.slice(0, 10); // Keep last 10 context messages
    logger.debug('Project context set:', projectInfo);
  }

  public async addFileContext(filePath: string, content: string): Promise<void> {
    const language = this.getLanguageFromPath(filePath);
    const contextMessage: PuterContextMessage = {
      role: 'system',
      content: `File: ${filePath}\n\`\`\`${language}\n${content.substring(0, 2000)}\n\`\`\``,
      metadata: {
        filePath,
        language,
        type: 'code',
        timestamp: new Date()
      }
    };
    
    this.contextMessages.push(contextMessage);
    this.contextMessages = this.contextMessages.slice(-20); // Keep last 20 context messages
    logger.debug('File context added:', filePath);
  }

  public async clearContext(): Promise<void> {
    this.contextMessages = [];
    logger.debug('Context cleared');
  }

  public async generateProject(description: string, requirements: string[]): Promise<ProjectStructure> {
    try {
      const prompt = `Generate a complete project structure for: ${description}\n\nRequirements:\n${requirements.map(r => `- ${r}`).join('\n')}\n\nProvide a detailed project structure with files, directories, dependencies, and configuration.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert software architect. Generate complete, production-ready project structures.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.4
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseProjectStructure(content, description);
      
    } catch (error) {
      logger.error('Failed to generate project:', error as Error);
      throw new Error(`Project generation failed: ${(error as any).message}`);
    }
  }

  public async debugCode(code: string, errorMessage: string): Promise<DebuggingHelp> {
    try {
      const prompt = `Debug the following code that's producing this error: "${errorMessage}"\n\nCode:\n\`\`\`\n${code}\n\`\`\`\n\nProvide analysis, possible causes, and solutions.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert debugger. Analyze code errors and provide clear solutions.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.3
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseDebuggingHelp(content);
      
    } catch (error) {
      logger.error('Failed to debug code:', error as Error);
      throw new Error(`Code debugging failed: ${(error as any).message}`);
    }
  }

  public async optimizeCode(code: string, language: string): Promise<OptimizedCode> {
    try {
      const prompt = `Optimize the following ${language} code for better performance, readability, and maintainability:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nProvide the optimized code and explain the improvements.`;
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an expert in code optimization. Improve code performance and quality while maintaining functionality.'
        },
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: 0.2
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseOptimizedCode(content, code);
      
    } catch (error) {
      logger.error('Failed to optimize code:', error as Error);
      throw new Error(`Code optimization failed: ${(error as any).message}`);
    }
  }

  private buildMessages(prompt: string, context?: CodeContext): any[] {
    const messages = [...this.contextMessages];
    
    if (context) {
      messages.push({
        role: 'system',
        content: `Current file: ${context.filePath} (${context.language})`
      });
      
      if (context.content) {
        messages.push({
          role: 'system',
          content: `File content:\n\`\`\`${context.language}\n${context.content}\n\`\`\``
        });
      }
    }
    
    messages.push({
      role: 'user',
      content: prompt
    });
    
    return messages;
  }

  private extractCodeFromResponse(response: string): string {
    // Extract code blocks from markdown
    const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
    const matches = response.match(codeBlockRegex);
    
    if (matches && matches.length > 0) {
      return matches[0].replace(/```[\w]*\n/, '').replace(/\n```$/, '');
    }
    
    return response;
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'java': 'java',
      'cs': 'csharp',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby'
    };
    
    return languageMap[extension || ''] || 'plaintext';
  }

  private parseProjectStructure(content: string, description: string): ProjectStructure {
    // This is a simplified parser - in a real implementation, you'd want more sophisticated parsing
    return {
      name: description.split(' ')[0].toLowerCase(),
      description,
      files: [],
      directories: [],
      dependencies: [],
      scripts: {},
      configuration: {}
    };
  }

  private parseDebuggingHelp(content: string): DebuggingHelp {
    return {
      analysis: content,
      possibleCauses: [],
      solutions: [],
      relatedDocs: []
    };
  }

  private parseOptimizedCode(content: string, originalCode: string): OptimizedCode {
    const optimizedCode = this.extractCodeFromResponse(content);
    
    return {
      originalCode,
      optimizedCode,
      improvements: [],
      performanceGain: 'Estimated improvement',
      explanation: content
    };
  }
}
