{"name": "200model8extension", "displayName": "200Model8 - AI Development Assistant", "description": "Powerful AI-powered coding assistant with full file editing, code generation, and project management capabilities", "version": "1.0.0", "publisher": "jeff9497", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Programming Languages"], "keywords": ["ai", "assistant", "code generation", "development", "productivity"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "200model8.openChat", "title": "Open AI Chat", "icon": "$(comment-discussion)"}, {"command": "200model8.generateCode", "title": "Generate Code", "icon": "$(code)"}, {"command": "200model8.explainCode", "title": "Explain Code", "icon": "$(question)"}, {"command": "200model8.refactorCode", "title": "Refactor Code", "icon": "$(wrench)"}, {"command": "200model8.createFile", "title": "Create File with AI", "icon": "$(new-file)"}, {"command": "200model8.editFile", "title": "Edit File with AI", "icon": "$(edit)"}, {"command": "200model8.generateProject", "title": "Generate Project Structure", "icon": "$(folder)"}, {"command": "200model8.generateTests", "title": "Generate Tests", "icon": "$(beaker)"}, {"command": "200model8.debugCode", "title": "Debug Code with AI", "icon": "$(debug)"}], "views": {"explorer": [{"id": "200model8Chat", "name": "AI Assistant", "when": "true"}]}, "viewsWelcome": [{"view": "200model8Chat", "contents": "Welcome to 200Model8 AI Assistant!\n[Open Chat](command:200model8.openChat)\n[Generate Code](command:200model8.generateCode)\n[Create New Project](command:200model8.generateProject)"}], "menus": {"editor/context": [{"command": "200model8.explainCode", "when": "editorHasSelection", "group": "200model8@1"}, {"command": "200model8.refactorCode", "when": "editorHasSelection", "group": "200model8@2"}, {"command": "200model8.generateCode", "group": "200model8@3"}, {"command": "200model8.generateTests", "when": "editorHasSelection", "group": "200model8@4"}, {"command": "200model8.debugCode", "when": "editorHasSelection", "group": "200model8@5"}], "explorer/context": [{"command": "200model8.createFile", "when": "explorerResourceIsFolder", "group": "200model8@1"}, {"command": "200model8.editFile", "when": "explorerResourceIsFile", "group": "200model8@2"}], "view/title": [{"command": "200model8.openChat", "when": "view == 200model8Chat", "group": "navigation"}]}, "keybindings": [{"command": "200model8.openChat", "key": "ctrl+alt+8", "mac": "cmd+alt+8"}, {"command": "200model8.generateCode", "key": "ctrl+alt+g", "mac": "cmd+alt+g"}, {"command": "200model8.explainCode", "key": "ctrl+alt+e", "mac": "cmd+alt+e", "when": "editorHasSelection"}, {"command": "200model8.refactorCode", "key": "ctrl+alt+r", "mac": "cmd+alt+r", "when": "editorHasSelection"}], "configuration": {"title": "200Model8 Settings", "properties": {"200model8.puterApiKey": {"type": "string", "description": "Puter.js API key for AI model access", "default": ""}, "200model8.defaultModel": {"type": "string", "enum": ["gpt-4", "gpt-3.5-turbo", "claude-3-opus", "claude-3-sonnet", "gemini-pro"], "default": "gpt-4", "description": "Default AI model to use"}, "200model8.autoSave": {"type": "boolean", "default": true, "description": "Automatically save generated files"}, "200model8.contextLines": {"type": "number", "default": 100, "minimum": 10, "maximum": 1000, "description": "Number of context lines to send with AI requests"}, "200model8.enableLogging": {"type": "boolean", "default": false, "description": "Enable detailed logging for debugging"}, "200model8.maxTokens": {"type": "number", "default": 4000, "minimum": 100, "maximum": 8000, "description": "Maximum tokens for AI responses"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}