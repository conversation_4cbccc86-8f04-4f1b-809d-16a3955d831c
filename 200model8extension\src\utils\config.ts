import * as vscode from 'vscode';
import { ExtensionConfig } from '../types/extension';
import { logger } from './logger';

export class ConfigManager {
  private static instance: ConfigManager;
  private config: vscode.WorkspaceConfiguration;

  private constructor() {
    this.config = vscode.workspace.getConfiguration('200model8');
    this.setupConfigurationWatcher();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private setupConfigurationWatcher(): void {
    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration('200model8')) {
        this.config = vscode.workspace.getConfiguration('200model8');
        logger.info('Configuration updated');
      }
    });
  }

  public getConfig(): ExtensionConfig {
    return {
      aiProvider: this.config.get<string>('aiProvider', 'puter'),
      puterApiKey: this.config.get<string>('puterApiKey', ''),
      openRouterApiKey: this.config.get<string>('openRouterApiKey', ''),
      defaultModel: this.config.get<string>('defaultModel', 'gpt-4'),
      autoSave: this.config.get<boolean>('autoSave', true),
      contextLines: this.config.get<number>('contextLines', 100),
      enableLogging: this.config.get<boolean>('enableLogging', false),
      maxTokens: this.config.get<number>('maxTokens', 4000),
      showFreeModelsOnly: this.config.get<boolean>('showFreeModelsOnly', false),
      preferredModelTypes: this.config.get<string[]>('preferredModelTypes', ['chat', 'completion'])
    };
  }

  public async updateConfig(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {
    try {
      await this.config.update(key, value, target || vscode.ConfigurationTarget.Global);
      logger.info(`Configuration updated: ${key} = ${value}`);
    } catch (error) {
      logger.error(`Failed to update configuration: ${key}`, error as Error);
      throw error;
    }
  }

  public get<T>(key: string, defaultValue: T): T {
    return this.config.get<T>(key, defaultValue);
  }

  public has(key: string): boolean {
    return this.config.has(key);
  }

  public async validateConfig(): Promise<boolean> {
    const config = this.getConfig();
    const issues: string[] = [];

    // Validate API key
    if (!config.puterApiKey || config.puterApiKey.trim() === '') {
      issues.push('Puter API key is not configured');
    }

    // Validate model
    const validModels = ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'gemini-pro'];
    if (!validModels.includes(config.defaultModel)) {
      issues.push(`Invalid default model: ${config.defaultModel}`);
    }

    // Validate numeric values
    if (config.contextLines < 10 || config.contextLines > 1000) {
      issues.push('Context lines must be between 10 and 1000');
    }

    if (config.maxTokens < 100 || config.maxTokens > 8000) {
      issues.push('Max tokens must be between 100 and 8000');
    }

    if (issues.length > 0) {
      logger.warn('Configuration validation failed:', issues);
      
      const action = await vscode.window.showWarningMessage(
        `Configuration issues found:\n${issues.join('\n')}`,
        'Open Settings',
        'Ignore'
      );

      if (action === 'Open Settings') {
        vscode.commands.executeCommand('workbench.action.openSettings', '200model8');
      }

      return false;
    }

    return true;
  }

  public async promptForApiKey(): Promise<string | undefined> {
    const apiKey = await vscode.window.showInputBox({
      prompt: 'Enter your Puter.js API key',
      password: true,
      ignoreFocusOut: true,
      validateInput: (value) => {
        if (!value || value.trim() === '') {
          return 'API key cannot be empty';
        }
        return null;
      }
    });

    if (apiKey) {
      await this.updateConfig('puterApiKey', apiKey);
      return apiKey;
    }

    return undefined;
  }

  public async selectModel(): Promise<string | undefined> {
    const models = [
      { label: 'GPT-4', value: 'gpt-4', description: 'Most capable model' },
      { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo', description: 'Fast and efficient' },
      { label: 'Claude 3 Opus', value: 'claude-3-opus', description: 'Excellent for code analysis' },
      { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet', description: 'Balanced performance' },
      { label: 'Gemini Pro', value: 'gemini-pro', description: 'Google\'s latest model' }
    ];

    const selected = await vscode.window.showQuickPick(models, {
      placeHolder: 'Select AI model',
      ignoreFocusOut: true
    });

    if (selected) {
      await this.updateConfig('defaultModel', selected.value);
      return selected.value;
    }

    return undefined;
  }

  public getWorkspaceConfig(): any {
    return {
      workspacePath: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath,
      workspaceName: vscode.workspace.name,
      language: vscode.window.activeTextEditor?.document.languageId,
      openFiles: vscode.workspace.textDocuments.map(doc => ({
        path: doc.uri.fsPath,
        language: doc.languageId,
        isDirty: doc.isDirty
      }))
    };
  }
}

export const configManager = ConfigManager.getInstance();
