import axios from 'axios';

// Reed API configuration from environment variables
const REED_API_KEY = import.meta.env.VITE_REED_API_KEY;
const REED_BASE_URL = 'https://www.reed.co.uk/api/1.0';

// Create axios instance with Reed API configuration
const reedApi = axios.create({
  baseURL: REED_BASE_URL,
  auth: {
    username: REED_API_KEY,
    password: ''
  },
  headers: {
    'Content-Type': 'application/json',
  }
});

// No fallback data - API must work or fail properly

/**
 * Search for jobs using Reed API
 * @param {Object} params - Search parameters
 * @param {string} params.keywords - Job keywords to search for
 * @param {string} params.locationName - Location to search in
 * @param {number} params.resultsToTake - Number of results to return
 * @param {number} params.resultsToSkip - Number of results to skip for pagination
 * @returns {Promise} API response with job listings
 */
export const searchJobs = async (params = {}) => {
  try {
    // Check if API key is configured
    if (!REED_API_KEY) {
      throw new Error('Reed API key is not configured');
    }

    const { keywords = '', locationName = '', resultsToTake = 20, resultsToSkip = 0 } = params;

    // Build query parameters
    const queryParams = new URLSearchParams();

    if (keywords) queryParams.append('keywords', keywords);
    if (locationName) queryParams.append('locationName', locationName);
    queryParams.append('resultsToTake', resultsToTake.toString());
    queryParams.append('resultsToSkip', resultsToSkip.toString());

    // Make API request to Reed
    const response = await reedApi.get(`/search?${queryParams.toString()}`);

    if (response.data && response.data.results) {
      return {
        success: true,
        data: response.data,
        totalResults: response.data.totalResults || response.data.results.length,
        jobs: response.data.results
      };
    } else {
      throw new Error('Invalid response format from Reed API');
    }

  } catch (error) {
    console.error('Reed API Error:', error);

    let errorMessage = 'Failed to fetch jobs from Reed API';

    if (error.message.includes('CORS') || error.message.includes('Network Error')) {
      errorMessage = 'Unable to connect to Reed API. This is likely due to CORS restrictions in development. The API will work properly when deployed to production (Vercel/Netlify).';
    } else if (error.response?.status === 401) {
      errorMessage = 'Invalid Reed API key. Please check your VITE_REED_API_KEY environment variable.';
    } else if (error.response?.status === 429) {
      errorMessage = 'Reed API rate limit exceeded. Please try again later.';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    return {
      success: false,
      error: errorMessage,
      jobs: [],
      totalResults: 0,
      data: { results: [], totalResults: 0 }
    };
  }
};

/**
 * Get job details by ID using Reed API
 * @param {string} jobId - Job ID
 * @returns {Promise} Job details
 */
export const getJobDetails = async (jobId) => {
  try {
    // Check if API key is configured
    if (!REED_API_KEY) {
      throw new Error('Reed API key is not configured');
    }

    // Make API request to Reed for job details
    const response = await reedApi.get(`/jobs/${jobId}`);

    if (response.data) {
      return {
        success: true,
        data: response.data
      };
    } else {
      throw new Error('Job not found');
    }

  } catch (error) {
    console.error('Reed API Job Details Error:', error);

    let errorMessage = 'Failed to fetch job details';

    if (error.message.includes('CORS') || error.message.includes('Network Error')) {
      errorMessage = 'Unable to connect to Reed API due to CORS restrictions in development. This will work in production.';
    } else if (error.response?.status === 404) {
      errorMessage = 'Job not found or may have been removed';
    } else if (error.response?.status === 401) {
      errorMessage = 'Invalid Reed API key';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * Get popular job categories and locations
 * This is a helper function to provide common search options
 */
export const getJobCategories = () => {
  return [
    'Software Developer',
    'Data Scientist',
    'Product Manager',
    'UX Designer',
    'Marketing Manager',
    'Sales Executive',
    'Business Analyst',
    'Project Manager',
    'DevOps Engineer',
    'Frontend Developer',
    'Backend Developer',
    'Full Stack Developer',
    'Mobile Developer',
    'QA Engineer',
    'Cybersecurity Specialist'
  ];
};

export const getPopularLocations = () => {
  return [
    'London',
    'Manchester',
    'Birmingham',
    'Leeds',
    'Glasgow',
    'Bristol',
    'Edinburgh',
    'Liverpool',
    'Newcastle',
    'Sheffield',
    'Remote',
    'Hybrid'
  ];
};

/**
 * Test Reed API connection
 * @returns {Promise} API connection test result
 */
export const testReedApiConnection = async () => {
  try {
    if (!REED_API_KEY) {
      return {
        success: false,
        error: 'Reed API key is not configured. Please add VITE_REED_API_KEY to your environment variables.'
      };
    }

    // Test with a simple search
    const response = await reedApi.get('/search?keywords=developer&resultsToTake=1');

    return {
      success: true,
      message: 'Reed API connection successful',
      totalJobs: response.data?.totalResults || 0
    };

  } catch (error) {
    return {
      success: false,
      error: error.message.includes('CORS') || error.message.includes('Network Error')
        ? 'CORS error in development - API will work in production'
        : error.response?.data?.message || error.message || 'Failed to connect to Reed API'
    };
  }
};

/**
 * Format salary for display
 * @param {number} min - Minimum salary
 * @param {number} max - Maximum salary
 * @returns {string} Formatted salary string
 */
export const formatSalary = (min, max) => {
  if (!min && !max) return 'Salary not specified';
  if (min && max) return `£${min.toLocaleString()} - £${max.toLocaleString()}`;
  if (min) return `From £${min.toLocaleString()}`;
  if (max) return `Up to £${max.toLocaleString()}`;
};

/**
 * Format job type for display
 * @param {Object} job - Job object from Reed API
 * @returns {string} Formatted job type
 */
export const formatJobType = (job) => {
  const types = [];
  if (job.fullTime) types.push('Full-time');
  if (job.partTime) types.push('Part-time');
  if (job.permanent) types.push('Permanent');
  if (job.contract) types.push('Contract');
  if (job.temp) types.push('Temporary');
  
  return types.length > 0 ? types.join(', ') : 'Not specified';
};
