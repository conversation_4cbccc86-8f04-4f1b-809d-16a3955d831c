import * as vscode from 'vscode';
import { PuterService } from '../services/puterService';
import { CodeParser } from '../utils/parser';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';

export class HoverProvider implements vscode.HoverProvider {
  private puterService: PuterService;

  constructor() {
    this.puterService = PuterService.getInstance();
  }

  public async provideHover(
    document: vscode.TextDocument,
    position: vscode.Position,
    token: vscode.CancellationToken
  ): Promise<vscode.Hover | undefined> {
    try {
      // Check if AI hover is enabled
      const config = configManager.getConfig();
      if (!config.puterApiKey) {
        return undefined;
      }

      // Get the word at the current position
      const wordRange = document.getWordRangeAtPosition(position);
      if (!wordRange) {
        return undefined;
      }

      const word = document.getText(wordRange);
      if (!word || word.length < 2) {
        return undefined;
      }

      // Get surrounding context
      const contextLines = 10;
      const startLine = Math.max(0, position.line - contextLines);
      const endLine = Math.min(document.lineCount - 1, position.line + contextLines);
      
      const contextRange = new vscode.Range(startLine, 0, endLine, 0);
      const contextText = document.getText(contextRange);

      // Parse the code to understand the symbol
      const parsedCode = CodeParser.parseCode(contextText, document.languageId);
      const symbolInfo = this.findSymbolInfo(word, parsedCode, position.line - startLine);

      if (!symbolInfo) {
        return undefined;
      }

      // Generate AI explanation
      const explanation = await this.generateExplanation(word, symbolInfo, contextText, document.languageId);
      
      if (!explanation) {
        return undefined;
      }

      const hoverContent = new vscode.MarkdownString();
      hoverContent.isTrusted = true;
      hoverContent.supportHtml = true;

      // Add symbol type and signature
      if (symbolInfo.type) {
        hoverContent.appendCodeblock(`${symbolInfo.type}: ${word}`, document.languageId);
      }

      // Add AI explanation
      hoverContent.appendMarkdown('\n\n🤖 **AI Explanation:**\n\n');
      hoverContent.appendMarkdown(explanation);

      // Add additional info if available
      if (symbolInfo.documentation) {
        hoverContent.appendMarkdown('\n\n**Documentation:**\n\n');
        hoverContent.appendMarkdown(symbolInfo.documentation);
      }

      if (symbolInfo.examples) {
        hoverContent.appendMarkdown('\n\n**Examples:**\n\n');
        hoverContent.appendCodeblock(symbolInfo.examples, document.languageId);
      }

      logger.debug(`Provided hover for symbol: ${word}`);
      return new vscode.Hover(hoverContent, wordRange);

    } catch (error) {
      logger.error('Failed to provide hover:', error as Error);
      return undefined;
    }
  }

  private findSymbolInfo(word: string, parsedCode: any, currentLine: number): any {
    // Look for the symbol in functions
    for (const func of parsedCode.functions) {
      if (func.name === word) {
        return {
          type: 'function',
          name: word,
          parameters: func.parameters,
          returnType: func.returnType,
          documentation: func.documentation,
          line: func.startLine
        };
      }
    }

    // Look for the symbol in classes
    for (const cls of parsedCode.classes) {
      if (cls.name === word) {
        return {
          type: 'class',
          name: word,
          methods: cls.methods,
          properties: cls.properties,
          documentation: cls.documentation,
          line: cls.startLine
        };
      }

      // Check class methods
      for (const method of cls.methods) {
        if (method.name === word) {
          return {
            type: 'method',
            name: word,
            className: cls.name,
            parameters: method.parameters,
            returnType: method.returnType,
            documentation: method.documentation,
            line: method.startLine
          };
        }
      }
    }

    // Look for the symbol in variables
    for (const variable of parsedCode.variables) {
      if (variable.name === word) {
        return {
          type: variable.isConst ? 'constant' : 'variable',
          name: word,
          variableType: variable.type,
          line: variable.line
        };
      }
    }

    // Look for the symbol in imports
    for (const imp of parsedCode.imports) {
      if (imp.imports.includes(word)) {
        return {
          type: 'import',
          name: word,
          module: imp.module,
          line: imp.line
        };
      }
    }

    return null;
  }

  private async generateExplanation(word: string, symbolInfo: any, context: string, language: string): Promise<string> {
    try {
      let prompt = `Explain the ${symbolInfo.type} "${word}" in this ${language} code context:\n\n`;
      
      // Add specific information based on symbol type
      switch (symbolInfo.type) {
        case 'function':
          prompt += `Function: ${word}\n`;
          if (symbolInfo.parameters && symbolInfo.parameters.length > 0) {
            prompt += `Parameters: ${symbolInfo.parameters.map((p: any) => p.name).join(', ')}\n`;
          }
          if (symbolInfo.returnType) {
            prompt += `Return type: ${symbolInfo.returnType}\n`;
          }
          break;

        case 'class':
          prompt += `Class: ${word}\n`;
          if (symbolInfo.methods && symbolInfo.methods.length > 0) {
            prompt += `Methods: ${symbolInfo.methods.map((m: any) => m.name).join(', ')}\n`;
          }
          break;

        case 'method':
          prompt += `Method: ${word} (in class ${symbolInfo.className})\n`;
          if (symbolInfo.parameters && symbolInfo.parameters.length > 0) {
            prompt += `Parameters: ${symbolInfo.parameters.map((p: any) => p.name).join(', ')}\n`;
          }
          break;

        case 'variable':
        case 'constant':
          prompt += `${symbolInfo.type}: ${word}\n`;
          if (symbolInfo.variableType) {
            prompt += `Type: ${symbolInfo.variableType}\n`;
          }
          break;

        case 'import':
          prompt += `Import: ${word} from ${symbolInfo.module}\n`;
          break;
      }

      prompt += `\nCode context:\n\`\`\`${language}\n${context.substring(0, 1000)}\n\`\`\`\n\n`;
      prompt += `Provide a concise explanation (2-3 sentences) of what this ${symbolInfo.type} does and how it's used.`;

      const explanation = await this.puterService.explainCode(prompt, language);
      
      // Clean up the explanation (remove code blocks if present)
      return explanation.replace(/```[\s\S]*?```/g, '').trim();

    } catch (error) {
      logger.error('Failed to generate explanation:', error as Error);
      return `This is a ${symbolInfo.type} named "${word}".`;
    }
  }
}
