import * as vscode from 'vscode';

// Core interfaces for the extension
export interface ExtensionConfig {
  puterApiKey: string;
  defaultModel: string;
  autoSave: boolean;
  contextLines: number;
  enableLogging: boolean;
  maxTokens: number;
}

export interface CodeContext {
  filePath: string;
  language: string;
  content: string;
  selection?: vscode.Range;
  cursorPosition?: vscode.Position;
  projectInfo?: ProjectInfo;
}

export interface ProjectInfo {
  name: string;
  rootPath: string;
  language: string;
  framework?: string;
  dependencies: string[];
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo' | 'go';
}

export interface AIResponse {
  content: string;
  model: string;
  tokens: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

export interface CodeAnalysis {
  complexity: number;
  issues: CodeIssue[];
  suggestions: string[];
  dependencies: string[];
  exports: string[];
  imports: string[];
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  line: number;
  column: number;
  severity: vscode.DiagnosticSeverity;
}

export interface BugReport {
  description: string;
  location: vscode.Range;
  severity: 'critical' | 'high' | 'medium' | 'low';
  suggestion: string;
  fixCode?: string;
}

export interface Optimization {
  type: 'performance' | 'memory' | 'readability' | 'maintainability';
  description: string;
  location: vscode.Range;
  originalCode: string;
  optimizedCode: string;
  impact: 'high' | 'medium' | 'low';
}

export interface CompletionItem extends vscode.CompletionItem {
  aiGenerated: boolean;
  confidence: number;
  context: string;
}

export interface ProjectStructure {
  name: string;
  description: string;
  files: ProjectFile[];
  directories: string[];
  dependencies: Dependency[];
  scripts: Record<string, string>;
  configuration: Record<string, any>;
}

export interface ProjectFile {
  path: string;
  content: string;
  type: 'source' | 'config' | 'documentation' | 'test';
  language: string;
}

export interface Dependency {
  name: string;
  version: string;
  type: 'production' | 'development';
  description?: string;
}

export interface Migration {
  description: string;
  steps: MigrationStep[];
  rollback: MigrationStep[];
  warnings: string[];
}

export interface MigrationStep {
  type: 'create' | 'modify' | 'delete' | 'rename';
  path: string;
  content?: string;
  newPath?: string;
}

export interface SearchOptions {
  includeFiles: string[];
  excludeFiles: string[];
  caseSensitive: boolean;
  wholeWord: boolean;
  regex: boolean;
  maxResults: number;
}

export interface SearchResult {
  file: string;
  line: number;
  column: number;
  text: string;
  context: string;
}

export interface ReplaceOptions extends SearchOptions {
  preview: boolean;
  backup: boolean;
}

export interface FileChangeEvent {
  type: 'created' | 'modified' | 'deleted' | 'renamed';
  path: string;
  newPath?: string;
  timestamp: Date;
}

export interface DebuggingHelp {
  analysis: string;
  possibleCauses: string[];
  solutions: Solution[];
  relatedDocs: string[];
}

export interface Solution {
  description: string;
  code?: string;
  steps: string[];
  confidence: number;
}

export interface OptimizedCode {
  originalCode: string;
  optimizedCode: string;
  improvements: string[];
  performanceGain: string;
  explanation: string;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  context?: CodeContext;
  attachments?: string[];
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  context: CodeContext[];
  model: string;
  created: Date;
  lastActivity: Date;
}

// Event types
export interface ExtensionEvents {
  onCodeGenerated: vscode.Event<{ code: string; context: CodeContext }>;
  onFileCreated: vscode.Event<{ path: string; content: string }>;
  onFileModified: vscode.Event<{ path: string; changes: string }>;
  onProjectGenerated: vscode.Event<{ structure: ProjectStructure }>;
  onError: vscode.Event<{ error: Error; context: string }>;
}

// Service interfaces
export interface IFileService {
  createFile(path: string, content: string): Promise<void>;
  readFile(path: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;
  deleteFile(path: string): Promise<void>;
  renameFile(oldPath: string, newPath: string): Promise<void>;
  createDirectory(path: string): Promise<void>;
  readDirectory(path: string): Promise<string[]>;
  deleteDirectory(path: string): Promise<void>;
  createMultipleFiles(files: { path: string; content: string }[]): Promise<void>;
  updateMultipleFiles(updates: { path: string; content: string }[]): Promise<void>;
  createProject(name: string, template: string): Promise<void>;
  addDependency(packageName: string, version?: string): Promise<void>;
  updatePackageJson(updates: any): Promise<void>;
  searchInFiles(query: string, options?: SearchOptions): Promise<SearchResult[]>;
  replaceInFiles(query: string, replacement: string, options?: ReplaceOptions): Promise<void>;
  watchFile(path: string, callback: (event: FileChangeEvent) => void): void;
  unwatchFile(path: string): void;
}

export interface ICodeService {
  generateFunction(description: string, language: string): Promise<string>;
  generateClass(description: string, language: string): Promise<string>;
  generateComponent(description: string, framework: string): Promise<string>;
  generateTests(codeContent: string, language: string): Promise<string>;
  analyzeCode(code: string, language: string): Promise<CodeAnalysis>;
  findBugs(code: string, language: string): Promise<BugReport[]>;
  suggestOptimizations(code: string, language: string): Promise<Optimization[]>;
  insertCode(filePath: string, position: number, code: string): Promise<void>;
  replaceCode(filePath: string, startPos: number, endPos: number, newCode: string): Promise<void>;
  deleteCode(filePath: string, startPos: number, endPos: number): Promise<void>;
  refactorFunction(code: string, instructions: string): Promise<string>;
  extractMethod(code: string, selectionStart: number, selectionEnd: number): Promise<string>;
  renameSymbol(filePath: string, symbol: string, newName: string): Promise<void>;
  generateDocumentation(code: string, language: string): Promise<string>;
  generateComments(code: string, language: string): Promise<string>;
  provideCompletions(code: string, position: number, language: string): Promise<CompletionItem[]>;
  generateProjectStructure(description: string, language: string): Promise<ProjectStructure>;
  implementInterface(interfacePath: string, className: string): Promise<string>;
  createMigration(fromCode: string, toCode: string): Promise<Migration>;
}

export interface IPuterService {
  initializeConnection(): Promise<void>;
  switchModel(modelName: string): Promise<void>;
  getAvailableModels(): Promise<string[]>;
  generateCode(prompt: string, context?: CodeContext): Promise<string>;
  explainCode(code: string, language: string): Promise<string>;
  refactorCode(code: string, instructions: string): Promise<string>;
  generateTests(code: string, language: string): Promise<string>;
  streamResponse(prompt: string, onChunk: (chunk: string) => void): Promise<void>;
  setProjectContext(projectInfo: ProjectInfo): Promise<void>;
  addFileContext(filePath: string, content: string): Promise<void>;
  clearContext(): Promise<void>;
  generateProject(description: string, requirements: string[]): Promise<ProjectStructure>;
  debugCode(code: string, errorMessage: string): Promise<DebuggingHelp>;
  optimizeCode(code: string, language: string): Promise<OptimizedCode>;
}
