// Simple test script to verify OpenRouter API
import axios from 'axios';

const API_KEY = '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';
const BASE_URL = 'https://openrouter.ai/api/v1';

async function testOpenRouterAPI() {
  console.log('Testing OpenRouter API...');
  
  // Test 1: Check if we can fetch models
  try {
    console.log('\n1. Testing /models endpoint...');
    const modelsResponse = await axios.get(`${BASE_URL}/models`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Models endpoint works!');
    console.log(`Total models: ${modelsResponse.data.data.length}`);
    
    // Find free models
    const freeModels = modelsResponse.data.data.filter(model => 
      model.pricing && 
      model.pricing.prompt === '0' && 
      model.pricing.completion === '0'
    );
    
    console.log(`Free models: ${freeModels.length}`);
    console.log('First 5 free models:');
    freeModels.slice(0, 5).forEach(model => {
      console.log(`  - ${model.name} (${model.id})`);
    });
    
  } catch (error) {
    console.error('❌ Models endpoint failed:', error.response?.status, error.response?.statusText);
    console.error('Error data:', error.response?.data);
  }
  
  // Test 2: Try a simple chat completion with actual free models
  const testModels = [
    'google/gemma-3n-e2b-it:free',
    'tencent/hunyuan-a13b-instruct:free',
    'tngtech/deepseek-r1t2-chimera:free',
    'openrouter/cypher-alpha:free',
    'mistralai/mistral-small-3.2-24b-instruct:free'
  ];

  for (const model of testModels) {
    try {
      console.log(`\n2. Testing /chat/completions with ${model}...`);

      const chatResponse = await axios.post(`${BASE_URL}/chat/completions`, {
        model: model,
        messages: [
          {
            role: 'user',
            content: 'Hello! Can you respond with just "API test successful"?'
          }
        ],
        temperature: 0.7,
        max_tokens: 50
      }, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://job8earch.com',
          'X-Title': 'Job8earch'
        }
      });

      console.log(`✅ Chat completions with ${model} works!`);
      console.log('Response:', chatResponse.data.choices[0].message.content);
      break; // Success! Stop testing other models

    } catch (error) {
      console.error(`❌ ${model} failed:`, error.response?.status, error.response?.statusText);
      console.error('Error data:', error.response?.data);
    }
  }
}

// Run the test
testOpenRouterAPI().catch(console.error);
