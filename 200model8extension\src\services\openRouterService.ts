import axios, { AxiosInstance } from 'axios';
import * as vscode from 'vscode';
import { 
  IPuterService, 
  CodeContext, 
  ProjectInfo, 
  ProjectStructure, 
  DebuggingHelp, 
  OptimizedCode 
} from '../types/extension';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';
import { handleAPIError, handleNetworkError } from '../utils/errorHandler';

export interface OpenRouterModel {
  id: string;
  name: string;
  description: string;
  pricing: {
    prompt: string;
    completion: string;
  };
  context_length: number;
  architecture: {
    modality: string;
    tokenizer: string;
    instruct_type?: string;
  };
  top_provider: {
    context_length: number;
    max_completion_tokens: number;
  };
  per_request_limits?: {
    prompt_tokens: string;
    completion_tokens: string;
  };
}

export interface OpenRouterResponse {
  id: string;
  model: string;
  object: string;
  created: number;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenRouterService implements IPuterService {
  private static instance: OpenRouterService;
  private client: AxiosInstance;
  private apiKey: string;
  private availableModels: OpenRouterModel[] = [];
  private currentModel: string;
  private contextMessages: any[] = [];

  private constructor() {
    const config = configManager.getConfig();
    this.apiKey = config.openRouterApiKey || '';
    this.currentModel = config.defaultModel;
    this.initializeClient();
  }

  public static getInstance(): OpenRouterService {
    if (!OpenRouterService.instance) {
      OpenRouterService.instance = new OpenRouterService();
    }
    return OpenRouterService.instance;
  }

  private initializeClient(): void {
    this.client = axios.create({
      baseURL: 'https://openrouter.ai/api/v1',
      timeout: 60000,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://github.com/jeff9497/200model8extension',
        'X-Title': '200Model8 VS Code Extension'
      }
    });

    // Add request interceptor
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('OpenRouter API Request:', {
          url: config.url,
          method: config.method,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('OpenRouter API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('OpenRouter API Response:', {
          status: response.status,
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('OpenRouter API Response Error:', error);
        
        if (error.response?.status === 401) {
          vscode.window.showErrorMessage('Invalid OpenRouter API key. Please check your configuration.');
        } else if (error.response?.status === 429) {
          vscode.window.showWarningMessage('Rate limit exceeded. Please try again later.');
        } else if (error.response?.status >= 500) {
          vscode.window.showErrorMessage('OpenRouter service is temporarily unavailable.');
        }
        
        return Promise.reject(error);
      }
    );
  }

  public async initializeConnection(): Promise<void> {
    try {
      logger.info('Initializing OpenRouter connection...');
      
      if (!this.apiKey) {
        const apiKey = await this.promptForApiKey();
        if (!apiKey) {
          throw new Error('OpenRouter API key is required');
        }
        this.apiKey = apiKey;
        this.initializeClient();
      }

      // Test connection and load models
      await this.loadAvailableModels();
      logger.info('OpenRouter connection initialized successfully');
      
    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter connection initialization');
      throw error;
    }
  }

  public async loadAvailableModels(): Promise<void> {
    try {
      const response = await this.client.get('/models');
      this.availableModels = response.data.data || [];
      
      logger.info(`Loaded ${this.availableModels.length} OpenRouter models`);
    } catch (error) {
      handleAPIError(error as Error, 'Loading OpenRouter models');
      // Fallback to default models if API call fails
      this.availableModels = this.getDefaultModels();
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    if (this.availableModels.length === 0) {
      await this.loadAvailableModels();
    }

    const config = configManager.getConfig();
    let models = this.availableModels;

    // Filter by free models if requested
    if (config.showFreeModelsOnly) {
      models = models.filter(model => 
        model.pricing.prompt === '0' && model.pricing.completion === '0'
      );
    }

    // Filter by preferred model types
    if (config.preferredModelTypes && config.preferredModelTypes.length > 0) {
      models = models.filter(model => 
        config.preferredModelTypes.includes(model.architecture.modality)
      );
    }

    return models.map(model => model.id);
  }

  public async getFreeModels(): Promise<OpenRouterModel[]> {
    if (this.availableModels.length === 0) {
      await this.loadAvailableModels();
    }

    return this.availableModels.filter(model => 
      model.pricing.prompt === '0' && model.pricing.completion === '0'
    );
  }

  public async getModelDetails(modelId: string): Promise<OpenRouterModel | undefined> {
    if (this.availableModels.length === 0) {
      await this.loadAvailableModels();
    }

    return this.availableModels.find(model => model.id === modelId);
  }

  public async switchModel(modelName: string): Promise<void> {
    try {
      const availableModels = await this.getAvailableModels();
      if (!availableModels.includes(modelName)) {
        throw new Error(`Model ${modelName} is not available`);
      }
      
      this.currentModel = modelName;
      await configManager.updateConfig('defaultModel', modelName);
      logger.info(`Switched to OpenRouter model: ${modelName}`);
      
    } catch (error) {
      handleAPIError(error as Error, `Switching to model ${modelName}`);
      throw error;
    }
  }

  public async generateCode(prompt: string, context?: CodeContext): Promise<string> {
    try {
      const messages = this.buildMessages(prompt, context);
      
      const request = {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.7,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      };

      const response = await this.client.post('/chat/completions', request);
      const content = response.data.choices[0]?.message?.content || '';
      
      logger.info('Code generated successfully with OpenRouter');
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter code generation');
      throw new Error(`Code generation failed: ${(error as any).message}`);
    }
  }

  public async explainCode(code: string, language: string): Promise<string> {
    try {
      const prompt = `Please explain the following ${language} code in detail:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nProvide a clear explanation of what this code does, how it works, and any important details.`;
      
      const messages = [
        {
          role: 'system',
          content: 'You are an expert code reviewer and teacher. Provide clear, detailed explanations of code.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.3
      });

      return response.data.choices[0]?.message?.content || 'Unable to explain code.';
      
    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter code explanation');
      throw new Error(`Code explanation failed: ${(error as any).message}`);
    }
  }

  public async refactorCode(code: string, instructions: string): Promise<string> {
    try {
      const prompt = `Please refactor the following code according to these instructions: ${instructions}\n\nOriginal code:\n\`\`\`\n${code}\n\`\`\`\n\nProvide only the refactored code without explanations.`;
      
      const messages = [
        {
          role: 'system',
          content: 'You are an expert software engineer. Refactor code according to the given instructions while maintaining functionality.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.2
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter code refactoring');
      throw new Error(`Code refactoring failed: ${(error as any).message}`);
    }
  }

  public async generateTests(code: string, language: string): Promise<string> {
    try {
      const testFramework = this.getTestFramework(language);
      const prompt = `Generate comprehensive unit tests for the following ${language} code using ${testFramework}:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nInclude test cases for normal operation, edge cases, and error conditions. Use appropriate testing framework for ${language}.`;
      
      const messages = [
        {
          role: 'system',
          content: 'You are an expert in test-driven development. Generate comprehensive, well-structured unit tests.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.3
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.extractCodeFromResponse(content);
      
    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter test generation');
      throw new Error(`Test generation failed: ${(error as any).message}`);
    }
  }

  public async streamResponse(prompt: string, onChunk: (chunk: string) => void): Promise<void> {
    try {
      const messages = this.buildMessages(prompt);

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.7,
        stream: true
      }, {
        responseType: 'stream'
      });

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                onChunk(content);
              }
            } catch (e) {
              // Ignore parsing errors for streaming
            }
          }
        }
      });

    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter streaming response');
      throw error;
    }
  }

  public async setProjectContext(projectInfo: ProjectInfo): Promise<void> {
    const contextMessage = {
      role: 'system',
      content: `Project Context: ${projectInfo.name} (${projectInfo.language}${projectInfo.framework ? `, ${projectInfo.framework}` : ''}). Dependencies: ${projectInfo.dependencies.join(', ')}`,
      metadata: {
        type: 'instruction',
        timestamp: new Date()
      }
    };

    this.contextMessages.unshift(contextMessage);
    this.contextMessages = this.contextMessages.slice(0, 10);
    logger.debug('Project context set for OpenRouter:', projectInfo);
  }

  public async addFileContext(filePath: string, content: string): Promise<void> {
    const language = this.getLanguageFromPath(filePath);
    const contextMessage = {
      role: 'system',
      content: `File: ${filePath}\n\`\`\`${language}\n${content.substring(0, 2000)}\n\`\`\``,
      metadata: {
        filePath,
        language,
        type: 'code',
        timestamp: new Date()
      }
    };

    this.contextMessages.push(contextMessage);
    this.contextMessages = this.contextMessages.slice(-20);
    logger.debug('File context added for OpenRouter:', filePath);
  }

  public async clearContext(): Promise<void> {
    this.contextMessages = [];
    logger.debug('OpenRouter context cleared');
  }

  public async generateProject(description: string, requirements: string[]): Promise<ProjectStructure> {
    try {
      const prompt = `Generate a complete project structure for: ${description}\n\nRequirements:\n${requirements.map(r => `- ${r}`).join('\n')}\n\nProvide a detailed project structure with files, directories, dependencies, and configuration.`;

      const messages = [
        {
          role: 'system',
          content: 'You are an expert software architect. Generate complete, production-ready project structures.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.4
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseProjectStructure(content, description);

    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter project generation');
      throw new Error(`Project generation failed: ${(error as any).message}`);
    }
  }

  public async debugCode(code: string, errorMessage: string): Promise<DebuggingHelp> {
    try {
      const prompt = `Debug the following code that's producing this error: "${errorMessage}"\n\nCode:\n\`\`\`\n${code}\n\`\`\`\n\nProvide analysis, possible causes, and solutions.`;

      const messages = [
        {
          role: 'system',
          content: 'You are an expert debugger. Analyze code errors and provide clear solutions.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.3
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseDebuggingHelp(content);

    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter code debugging');
      throw new Error(`Code debugging failed: ${(error as any).message}`);
    }
  }

  public async optimizeCode(code: string, language: string): Promise<OptimizedCode> {
    try {
      const prompt = `Optimize the following ${language} code for better performance, readability, and maintainability:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nProvide the optimized code and explain the improvements.`;

      const messages = [
        {
          role: 'system',
          content: 'You are an expert in code optimization. Improve code performance and quality while maintaining functionality.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const response = await this.client.post('/chat/completions', {
        model: this.currentModel,
        messages,
        max_tokens: configManager.getConfig().maxTokens,
        temperature: 0.2
      });

      const content = response.data.choices[0]?.message?.content || '';
      return this.parseOptimizedCode(content, code);

    } catch (error) {
      handleAPIError(error as Error, 'OpenRouter code optimization');
      throw new Error(`Code optimization failed: ${(error as any).message}`);
    }
  }

  // Helper methods
  private async promptForApiKey(): Promise<string | undefined> {
    const apiKey = await vscode.window.showInputBox({
      prompt: 'Enter your OpenRouter API key',
      password: true,
      ignoreFocusOut: true,
      validateInput: (value) => {
        if (!value || value.trim() === '') {
          return 'API key cannot be empty';
        }
        return null;
      }
    });

    if (apiKey) {
      await configManager.updateConfig('openRouterApiKey', apiKey);
      return apiKey;
    }

    return undefined;
  }

  private buildMessages(prompt: string, context?: CodeContext): any[] {
    const messages = [...this.contextMessages];

    if (context) {
      messages.push({
        role: 'system',
        content: `Current file: ${context.filePath} (${context.language})`
      });

      if (context.content) {
        messages.push({
          role: 'system',
          content: `File content:\n\`\`\`${context.language}\n${context.content}\n\`\`\``
        });
      }
    }

    messages.push({
      role: 'user',
      content: prompt
    });

    return messages;
  }

  private extractCodeFromResponse(response: string): string {
    const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/g;
    const matches = response.match(codeBlockRegex);

    if (matches && matches.length > 0) {
      return matches[0].replace(/```[\w]*\n/, '').replace(/\n```$/, '');
    }

    return response;
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'java': 'java',
      'cs': 'csharp',
      'cpp': 'cpp',
      'c': 'c',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby'
    };

    return languageMap[extension || ''] || 'plaintext';
  }

  private getTestFramework(language: string): string {
    const testFrameworks: { [key: string]: string } = {
      'javascript': 'Jest',
      'typescript': 'Jest',
      'python': 'pytest',
      'java': 'JUnit',
      'csharp': 'NUnit',
      'go': 'testing',
      'rust': 'cargo test',
      'php': 'PHPUnit'
    };

    return testFrameworks[language] || 'Jest';
  }

  private parseProjectStructure(content: string, description: string): ProjectStructure {
    return {
      name: description.split(' ')[0].toLowerCase(),
      description,
      files: [],
      directories: [],
      dependencies: [],
      scripts: {},
      configuration: {}
    };
  }

  private parseDebuggingHelp(content: string): DebuggingHelp {
    return {
      analysis: content,
      possibleCauses: [],
      solutions: [],
      relatedDocs: []
    };
  }

  private parseOptimizedCode(content: string, originalCode: string): OptimizedCode {
    const optimizedCode = this.extractCodeFromResponse(content);

    return {
      originalCode,
      optimizedCode,
      improvements: [],
      performanceGain: 'Estimated improvement',
      explanation: content
    };
  }

  private getDefaultModels(): OpenRouterModel[] {
    return [
      {
        id: 'openai/gpt-4',
        name: 'GPT-4',
        description: 'OpenAI GPT-4',
        pricing: { prompt: '0.03', completion: '0.06' },
        context_length: 8192,
        architecture: { modality: 'chat', tokenizer: 'cl100k_base' },
        top_provider: { context_length: 8192, max_completion_tokens: 4096 }
      },
      {
        id: 'openai/gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'OpenAI GPT-3.5 Turbo',
        pricing: { prompt: '0.001', completion: '0.002' },
        context_length: 4096,
        architecture: { modality: 'chat', tokenizer: 'cl100k_base' },
        top_provider: { context_length: 4096, max_completion_tokens: 4096 }
      }
    ];
  }
}
