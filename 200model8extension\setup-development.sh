#!/bin/bash

# 200Model8 Extension - Complete Development Setup Script
echo "🚀 200Model8 Extension - Development Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_status "Node.js is installed: $NODE_VERSION"
else
    print_error "Node.js is not installed!"
    echo "Please install Node.js from: https://nodejs.org/"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_status "npm is installed: $NPM_VERSION"
else
    print_error "npm is not installed!"
    exit 1
fi

# Check Git
if command_exists git; then
    GIT_VERSION=$(git --version)
    print_status "Git is installed: $GIT_VERSION"
else
    print_error "Git is not installed!"
    echo "Please install Git from: https://git-scm.com/"
    exit 1
fi

# Check VS Code
if command_exists code; then
    print_status "VS Code CLI is available"
else
    print_warning "VS Code CLI not found. Make sure VS Code is installed and added to PATH"
fi

echo ""
echo -e "${BLUE}📦 Installing dependencies...${NC}"

# Install project dependencies
npm install

if [ $? -eq 0 ]; then
    print_status "Project dependencies installed successfully"
else
    print_error "Failed to install project dependencies"
    exit 1
fi

# Install global tools
echo ""
echo -e "${BLUE}🛠️  Installing global development tools...${NC}"

# Install vsce (VS Code Extension Manager)
if ! command_exists vsce; then
    npm install -g vsce
    if [ $? -eq 0 ]; then
        print_status "vsce installed globally"
    else
        print_warning "Failed to install vsce globally. You may need to run with sudo or check permissions"
    fi
else
    print_status "vsce is already installed"
fi

# Install TypeScript globally (optional)
if ! command_exists tsc; then
    npm install -g typescript
    if [ $? -eq 0 ]; then
        print_status "TypeScript installed globally"
    else
        print_warning "Failed to install TypeScript globally"
    fi
else
    print_status "TypeScript is already installed"
fi

# Install ESLint globally (optional)
if ! command_exists eslint; then
    npm install -g eslint
    if [ $? -eq 0 ]; then
        print_status "ESLint installed globally"
    else
        print_warning "Failed to install ESLint globally"
    fi
else
    print_status "ESLint is already installed"
fi

# Compile TypeScript
echo ""
echo -e "${BLUE}🔨 Compiling TypeScript...${NC}"
npm run compile

if [ $? -eq 0 ]; then
    print_status "TypeScript compilation successful"
else
    print_error "TypeScript compilation failed"
    exit 1
fi

# Run linter
echo ""
echo -e "${BLUE}🔍 Running linter...${NC}"
npm run lint

if [ $? -eq 0 ]; then
    print_status "Linting passed"
else
    print_warning "Linting found issues. Check the output above."
fi

# Install recommended VS Code extensions
echo ""
echo -e "${BLUE}🔌 Installing recommended VS Code extensions...${NC}"

if command_exists code; then
    # List of recommended extensions
    extensions=(
        "ms-vscode.vscode-typescript-next"
        "dbaeumer.vscode-eslint"
        "ms-vscode.test-adapter-converter"
        "eamodio.gitlens"
        "ms-vscode.vscode-json"
    )
    
    for ext in "${extensions[@]}"; do
        code --install-extension "$ext" --force
        if [ $? -eq 0 ]; then
            print_status "Installed extension: $ext"
        else
            print_warning "Failed to install extension: $ext"
        fi
    done
else
    print_warning "VS Code CLI not available. Please install extensions manually:"
    echo "  - TypeScript and JavaScript Language Features"
    echo "  - ESLint"
    echo "  - Test Explorer UI"
    echo "  - GitLens"
fi

# Setup Git configuration
echo ""
echo -e "${BLUE}📝 Configuring Git...${NC}"

# Check if Git user is configured
if [ -z "$(git config --global user.name)" ]; then
    git config --global user.name "jeff9497"
    print_status "Git user.name configured"
else
    print_status "Git user.name already configured: $(git config --global user.name)"
fi

if [ -z "$(git config --global user.email)" ]; then
    git config --global user.email "<EMAIL>"
    print_status "Git user.email configured"
else
    print_status "Git user.email already configured: $(git config --global user.email)"
fi

# Create .env template if it doesn't exist
echo ""
echo -e "${BLUE}⚙️  Setting up environment...${NC}"

if [ ! -f ".env" ]; then
    cat > .env << EOL
# 200Model8 Extension - Environment Variables
# Copy this file and add your actual API keys

# Puter.js API Key
PUTER_API_KEY=your_puter_api_key_here

# OpenRouter API Key  
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Development settings
NODE_ENV=development
DEBUG=true
EOL
    print_status "Created .env template file"
    print_info "Please edit .env file and add your actual API keys"
else
    print_status ".env file already exists"
fi

# Final setup verification
echo ""
echo -e "${BLUE}🧪 Running setup verification...${NC}"

# Test compilation
if npm run compile > /dev/null 2>&1; then
    print_status "Compilation test passed"
else
    print_error "Compilation test failed"
fi

# Test linting
if npm run lint > /dev/null 2>&1; then
    print_status "Linting test passed"
else
    print_warning "Linting test found issues"
fi

echo ""
echo -e "${GREEN}🎉 Setup Complete!${NC}"
echo "=========================================="
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Open VS Code: ${YELLOW}code .${NC}"
echo "2. Edit .env file with your API keys"
echo "3. Press ${YELLOW}F5${NC} to launch Extension Development Host"
echo "4. Test your extension in the new VS Code window"
echo ""
echo -e "${BLUE}🔗 Useful Commands:${NC}"
echo "• ${YELLOW}npm run dev${NC}        - Start development mode"
echo "• ${YELLOW}npm run watch${NC}      - Watch for changes"
echo "• ${YELLOW}npm test${NC}           - Run tests"
echo "• ${YELLOW}npm run package${NC}    - Create .vsix package"
echo ""
echo -e "${BLUE}📚 Documentation:${NC}"
echo "• README.md         - Main documentation"
echo "• DEVELOPMENT.md    - Development guide"
echo "• BUILD.md          - Build and deployment guide"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
