import * as vscode from 'vscode';
import { PuterService } from './services/puterService';
import { FileService } from './services/fileService';
import { CodeService } from './services/codeService';
import { ChatProvider } from './providers/chatProvider';
import { CompletionProvider } from './providers/completionProvider';
import { HoverProvider } from './providers/hoverProvider';
import { CodeActionProvider } from './providers/codeActionProvider';
import { DefinitionProvider } from './providers/definitionProvider';
import { ChatView } from './views/chatView';
import { logger, LogLevel } from './utils/logger';
import { configManager } from './utils/config';

// Command imports
import { generateCodeCommand } from './commands/generateCode';
import { createFileCommand } from './commands/createFile';
import { editFileCommand } from './commands/editFile';
import { generateProjectCommand } from './commands/generateProject';
import { explainCodeCommand } from './commands/explainCode';
import { refactorCodeCommand } from './commands/refactorCode';

export async function activate(context: vscode.ExtensionContext) {
  logger.info('200Model8 Extension is activating...');

  try {
    // Initialize configuration
    await initializeConfiguration();

    // Initialize services
    const puterService = PuterService.getInstance();
    const fileService = FileService.getInstance();
    const codeService = CodeService.getInstance();

    // Initialize AI connection
    await puterService.initializeConnection();

    // Register providers
    registerProviders(context);

    // Register commands
    registerCommands(context);

    // Initialize views
    initializeViews(context);

    // Set up event listeners
    setupEventListeners(context);

    // Show welcome message
    showWelcomeMessage();

    logger.info('200Model8 Extension activated successfully');

  } catch (error) {
    logger.error('Failed to activate extension:', error as Error);
    vscode.window.showErrorMessage(`Failed to activate 200Model8 Extension: ${(error as any).message}`);
  }
}

export function deactivate() {
  logger.info('200Model8 Extension is deactivating...');
  
  // Clean up services
  const fileService = FileService.getInstance();
  fileService.dispose();

  logger.info('200Model8 Extension deactivated');
}

async function initializeConfiguration(): Promise<void> {
  const config = configManager.getConfig();
  
  // Set logging level based on configuration
  if (config.enableLogging) {
    logger.setLogLevel(LogLevel.DEBUG);
  } else {
    logger.setLogLevel(LogLevel.INFO);
  }

  // Validate configuration
  const isValid = await configManager.validateConfig();
  if (!isValid) {
    logger.warn('Configuration validation failed');
  }
}

function registerProviders(context: vscode.ExtensionContext): void {
  logger.info('Registering providers...');

  // Language support for multiple languages
  const supportedLanguages = [
    'javascript',
    'typescript',
    'python',
    'java',
    'csharp',
    'cpp',
    'c',
    'go',
    'rust',
    'php',
    'ruby'
  ];

  // Register completion provider
  const completionProvider = new CompletionProvider();
  for (const language of supportedLanguages) {
    const completionDisposable = vscode.languages.registerCompletionItemProvider(
      language,
      completionProvider,
      '.', // Trigger on dot
      ' ', // Trigger on space
      '(' // Trigger on opening parenthesis
    );
    context.subscriptions.push(completionDisposable);
  }

  // Register hover provider
  const hoverProvider = new HoverProvider();
  for (const language of supportedLanguages) {
    const hoverDisposable = vscode.languages.registerHoverProvider(language, hoverProvider);
    context.subscriptions.push(hoverDisposable);
  }

  // Register code action provider
  const codeActionProvider = new CodeActionProvider();
  for (const language of supportedLanguages) {
    const codeActionDisposable = vscode.languages.registerCodeActionsProvider(
      language,
      codeActionProvider,
      {
        providedCodeActionKinds: [
          vscode.CodeActionKind.QuickFix,
          vscode.CodeActionKind.Refactor,
          vscode.CodeActionKind.RefactorExtract,
          vscode.CodeActionKind.RefactorInline,
          vscode.CodeActionKind.RefactorRewrite
        ]
      }
    );
    context.subscriptions.push(codeActionDisposable);
  }

  // Register definition provider
  const definitionProvider = new DefinitionProvider();
  for (const language of supportedLanguages) {
    const definitionDisposable = vscode.languages.registerDefinitionProvider(language, definitionProvider);
    context.subscriptions.push(definitionDisposable);
  }

  logger.info('Providers registered successfully');
}

function registerCommands(context: vscode.ExtensionContext): void {
  logger.info('Registering commands...');

  const commands = [
    // Main commands
    {
      command: '200model8.openChat',
      callback: () => {
        const chatView = ChatView.getInstance();
        chatView.show();
      }
    },
    {
      command: '200model8.generateCode',
      callback: generateCodeCommand
    },
    {
      command: '200model8.createFile',
      callback: createFileCommand
    },
    {
      command: '200model8.editFile',
      callback: editFileCommand
    },
    {
      command: '200model8.generateProject',
      callback: generateProjectCommand
    },
    {
      command: '200model8.explainCode',
      callback: explainCodeCommand
    },
    {
      command: '200model8.refactorCode',
      callback: refactorCodeCommand
    },
    {
      command: '200model8.generateTests',
      callback: async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showWarningMessage('No active editor found');
          return;
        }

        const selection = editor.selection;
        const code = selection.isEmpty ? editor.document.getText() : editor.document.getText(selection);
        const language = editor.document.languageId;

        try {
          const codeService = CodeService.getInstance();
          const tests = await codeService.generateTests(code, language);
          
          // Create test file
          const fileName = editor.document.fileName;
          const testFileName = fileName.replace(/\.(js|ts|py|java)$/, '.test.$1');
          
          const fileService = FileService.getInstance();
          await fileService.createFile(testFileName, tests);
          
          vscode.window.showInformationMessage('Tests generated successfully!');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to generate tests: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.debugCode',
      callback: async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showWarningMessage('No active editor found');
          return;
        }

        const selection = editor.selection;
        const code = selection.isEmpty ? editor.document.getText() : editor.document.getText(selection);

        const errorMessage = await vscode.window.showInputBox({
          prompt: 'Enter the error message you\'re experiencing',
          placeHolder: 'e.g., TypeError: Cannot read property of undefined'
        });

        if (!errorMessage) return;

        try {
          const puterService = PuterService.getInstance();
          const debugHelp = await puterService.debugCode(code, errorMessage);
          
          // Show debug help in a new document
          const doc = await vscode.workspace.openTextDocument({
            content: `Debug Analysis:\n${debugHelp.analysis}\n\nPossible Causes:\n${debugHelp.possibleCauses.join('\n')}\n\nSolutions:\n${debugHelp.solutions.map(s => s.description).join('\n')}`,
            language: 'markdown'
          });
          
          await vscode.window.showTextDocument(doc);
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to debug code: ${(error as any).message}`);
        }
      }
    },
    // Utility commands
    {
      command: '200model8.switchModel',
      callback: async () => {
        try {
          const selectedModel = await configManager.selectModel();
          if (selectedModel) {
            const puterService = PuterService.getInstance();
            await puterService.switchModel(selectedModel);
            vscode.window.showInformationMessage(`Switched to model: ${selectedModel}`);
          }
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to switch model: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.showLogs',
      callback: () => {
        logger.show();
      }
    },
    {
      command: '200model8.clearContext',
      callback: async () => {
        try {
          const puterService = PuterService.getInstance();
          await puterService.clearContext();
          vscode.window.showInformationMessage('AI context cleared');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to clear context: ${(error as any).message}`);
        }
      }
    },
    // Code action commands
    {
      command: '200model8.applyAIFix',
      callback: async (uri: vscode.Uri, range: vscode.Range, problemCode: string, context: string, errorMessage: string, language: string) => {
        try {
          const puterService = PuterService.getInstance();
          const fixedCode = await puterService.debugCode(problemCode, errorMessage);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, fixedCode.solutions[0]?.code || problemCode);
          });

          vscode.window.showInformationMessage('AI fix applied');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to apply AI fix: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.extractMethod',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string) => {
        try {
          const codeService = CodeService.getInstance();
          const extractedMethod = await codeService.extractMethod(selectedText, range.start.line, range.end.line);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, extractedMethod);
          });

          vscode.window.showInformationMessage('Method extracted');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to extract method: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.refactorForReadability',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const codeService = CodeService.getInstance();
          const refactoredCode = await codeService.refactorFunction(selectedText, 'Improve readability and code clarity');

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, refactoredCode);
          });

          vscode.window.showInformationMessage('Code refactored for readability');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to refactor code: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.modernizeCode',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const codeService = CodeService.getInstance();
          const modernizedCode = await codeService.refactorFunction(selectedText, `Convert to modern ${language} syntax and best practices`);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, modernizedCode);
          });

          vscode.window.showInformationMessage('Code modernized');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to modernize code: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.generateFunctionAtCursor',
      callback: async (uri: vscode.Uri, position: vscode.Position, language: string) => {
        try {
          const description = await vscode.window.showInputBox({
            prompt: 'Describe the function to generate',
            placeHolder: 'e.g., A function that validates email addresses'
          });

          if (!description) return;

          const codeService = CodeService.getInstance();
          const generatedFunction = await codeService.generateFunction(description, language);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.insert(position, '\n' + generatedFunction + '\n');
          });

          vscode.window.showInformationMessage('Function generated');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to generate function: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.generateClassAtCursor',
      callback: async (uri: vscode.Uri, position: vscode.Position, language: string) => {
        try {
          const description = await vscode.window.showInputBox({
            prompt: 'Describe the class to generate',
            placeHolder: 'e.g., A class that manages user sessions'
          });

          if (!description) return;

          const codeService = CodeService.getInstance();
          const generatedClass = await codeService.generateClass(description, language);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.insert(position, '\n' + generatedClass + '\n');
          });

          vscode.window.showInformationMessage('Class generated');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to generate class: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.optimizePerformance',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const puterService = PuterService.getInstance();
          const optimizedCode = await puterService.optimizeCode(selectedText, language);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, optimizedCode.optimizedCode);
          });

          vscode.window.showInformationMessage('Code optimized for performance');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to optimize code: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.reduceComplexity',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const codeService = CodeService.getInstance();
          const simplifiedCode = await codeService.refactorFunction(selectedText, 'Reduce complexity and simplify the logic');

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, simplifiedCode);
          });

          vscode.window.showInformationMessage('Code complexity reduced');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to reduce complexity: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.addDocumentation',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const codeService = CodeService.getInstance();
          const documentation = await codeService.generateDocumentation(selectedText, language);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.insert(range.start, documentation + '\n');
          });

          vscode.window.showInformationMessage('Documentation added');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to add documentation: ${(error as any).message}`);
        }
      }
    },
    {
      command: '200model8.addComments',
      callback: async (uri: vscode.Uri, range: vscode.Range, selectedText: string, language: string) => {
        try {
          const codeService = CodeService.getInstance();
          const commentedCode = await codeService.generateComments(selectedText, language);

          const editor = await vscode.window.showTextDocument(uri);
          await editor.edit(editBuilder => {
            editBuilder.replace(range, commentedCode);
          });

          vscode.window.showInformationMessage('Comments added');
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to add comments: ${(error as any).message}`);
        }
      }
    }
  ];

  // Register all commands
  for (const cmd of commands) {
    const disposable = vscode.commands.registerCommand(cmd.command, cmd.callback);
    context.subscriptions.push(disposable);
  }

  logger.info('Commands registered successfully');
}

function initializeViews(context: vscode.ExtensionContext): void {
  logger.info('Initializing views...');

  // Initialize chat view
  const chatView = ChatView.getInstance();
  chatView.initialize(context);

  // Register tree data provider for the chat view
  const chatProvider = new ChatProvider();
  vscode.window.registerTreeDataProvider('200model8Chat', chatProvider);

  logger.info('Views initialized successfully');
}

function setupEventListeners(context: vscode.ExtensionContext): void {
  logger.info('Setting up event listeners...');

  // Listen for active editor changes to update context
  const onDidChangeActiveEditor = vscode.window.onDidChangeActiveTextEditor(async (editor) => {
    if (editor) {
      try {
        const puterService = PuterService.getInstance();
        await puterService.addFileContext(editor.document.fileName, editor.document.getText());
      } catch (error) {
        logger.error('Failed to update file context:', error as Error);
      }
    }
  });
  context.subscriptions.push(onDidChangeActiveEditor);

  // Listen for configuration changes
  const onDidChangeConfiguration = vscode.workspace.onDidChangeConfiguration(async (event) => {
    if (event.affectsConfiguration('200model8')) {
      logger.info('Configuration changed, reinitializing...');
      await initializeConfiguration();
    }
  });
  context.subscriptions.push(onDidChangeConfiguration);

  // Listen for workspace folder changes
  const onDidChangeWorkspaceFolders = vscode.workspace.onDidChangeWorkspaceFolders(async (event) => {
    if (event.added.length > 0) {
      logger.info('Workspace folder added, updating project context...');
      // Update project context when workspace changes
      const puterService = PuterService.getInstance();
      const workspaceConfig = configManager.getWorkspaceConfig();
      if (workspaceConfig.workspacePath) {
        await puterService.setProjectContext({
          name: workspaceConfig.workspaceName || 'Unknown',
          rootPath: workspaceConfig.workspacePath,
          language: workspaceConfig.language || 'unknown',
          dependencies: [],
          packageManager: 'npm'
        });
      }
    }
  });
  context.subscriptions.push(onDidChangeWorkspaceFolders);

  logger.info('Event listeners set up successfully');
}

async function showWelcomeMessage(): Promise<void> {
  const config = configManager.getConfig();
  
  if (!config.puterApiKey) {
    const action = await vscode.window.showInformationMessage(
      'Welcome to 200Model8! To get started, please configure your Puter API key.',
      'Configure API Key',
      'Later'
    );
    
    if (action === 'Configure API Key') {
      await configManager.promptForApiKey();
    }
  } else {
    vscode.window.showInformationMessage(
      '200Model8 Extension is ready! Use Ctrl+Alt+8 to open the AI chat.',
      'Open Chat'
    ).then(action => {
      if (action === 'Open Chat') {
        vscode.commands.executeCommand('200model8.openChat');
      }
    });
  }
}
