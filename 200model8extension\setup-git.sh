#!/bin/bash

# 200Model8 Extension - Git Setup Script
echo "🚀 Setting up Git repository for 200Model8 Extension..."

# Configure Git user (if not already configured)
git config --global user.name "jeff9497"
git config --global user.email "<EMAIL>"

# Initialize Git repository
echo "📁 Initializing Git repository..."
git init

# Add all files
echo "📝 Adding files to Git..."
git add .

# Create initial commit
echo "💾 Creating initial commit..."
git commit -m "🎉 Initial commit: 200Model8 VS Code Extension with dual AI provider support

Features:
- Dual AI provider support (Puter.js + OpenRouter)
- Comprehensive file operations
- Advanced code generation
- Interactive AI chat interface
- VS Code integration (IntelliSense, hover, code actions)
- Project management and generation
- Multi-language support
- Real-time streaming responses
- Error handling and recovery
- Comprehensive test suite

Ready for development and deployment! 🚀"

# Add remote repository
echo "🌐 Adding remote repository..."
git remote add origin https://github.com/jeff9497/200Model8Extension.git

# Create and push to main branch
echo "📤 Pushing to GitHub..."
git branch -M main
git push -u origin main

echo "✅ Git setup complete! Repository created at: https://github.com/jeff9497/200Model8Extension"
echo ""
echo "🔗 Next steps:"
echo "1. Visit: https://github.com/jeff9497/200Model8Extension"
echo "2. Set up branch protection rules (optional)"
echo "3. Configure GitHub secrets for CI/CD (optional)"
echo "4. Start developing! 🎯"
