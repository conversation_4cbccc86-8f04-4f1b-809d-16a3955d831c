import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { AIServiceManager } from '../services/aiServiceManager';
import { FileService } from '../services/fileService';
import { CodeService } from '../services/codeService';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';

export class ChatView {
  private static instance: ChatView;
  private panel: vscode.WebviewPanel | undefined;
  private context: vscode.ExtensionContext | undefined;
  private aiServiceManager: AIServiceManager;
  private fileService: FileService;
  private codeService: CodeService;

  private constructor() {
    this.aiServiceManager = AIServiceManager.getInstance();
    this.fileService = FileService.getInstance();
    this.codeService = CodeService.getInstance();
  }

  public static getInstance(): ChatView {
    if (!ChatView.instance) {
      ChatView.instance = new ChatView();
    }
    return ChatView.instance;
  }

  public initialize(context: vscode.ExtensionContext): void {
    this.context = context;
  }

  public show(): void {
    if (this.panel) {
      this.panel.reveal(vscode.ViewColumn.Beside);
      return;
    }

    this.panel = vscode.window.createWebviewPanel(
      '200model8Chat',
      '200Model8 AI Assistant',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context!.extensionPath, 'src', 'webview'))
        ]
      }
    );

    this.panel.webview.html = this.getWebviewContent();
    this.setupWebviewMessageHandling();

    this.panel.onDidDispose(() => {
      this.panel = undefined;
    });

    logger.info('Chat view opened');
  }

  private getWebviewContent(): string {
    if (!this.context) {
      throw new Error('ChatView not initialized');
    }

    const webviewPath = path.join(this.context.extensionPath, 'src', 'webview');
    const htmlPath = path.join(webviewPath, 'chat.html');
    const jsPath = path.join(webviewPath, 'chat.js');

    let html = fs.readFileSync(htmlPath, 'utf8');
    const js = fs.readFileSync(jsPath, 'utf8');

    // Inject the JavaScript directly into the HTML
    html = html.replace('</body>', `<script>${js}</script></body>`);

    return html;
  }

  private setupWebviewMessageHandling(): void {
    if (!this.panel) return;

    this.panel.webview.onDidReceiveMessage(async (message) => {
      try {
        switch (message.type) {
          case 'sendMessage':
            await this.handleSendMessage(message);
            break;

          case 'clearChat':
            await this.handleClearChat();
            break;

          case 'attachCurrentFile':
            await this.handleAttachCurrentFile();
            break;

          case 'clearContext':
            await this.handleClearContext();
            break;

          case 'switchModel':
            await this.handleSwitchModel(message.model);
            break;

          case 'switchProvider':
            await this.handleSwitchProvider(message.provider);
            break;

          case 'copyCode':
            await this.handleCopyCode(message.code);
            break;

          case 'applyCode':
            await this.handleApplyCode(message.code);
            break;

          case 'insertCode':
            await this.handleInsertCode(message.code);
            break;
        }
      } catch (error) {
        logger.error('Error handling webview message:', error as Error);
        this.sendErrorToWebview((error as any).message);
      }
    });
  }

  private async handleSendMessage(message: any): Promise<void> {
    try {
      const userMessage = message.message;
      const context = message.context;
      const model = message.model;

      logger.info(`Processing message: ${userMessage.substring(0, 100)}...`);

      // Switch model if different
      const currentConfig = configManager.getConfig();
      if (model !== currentConfig.defaultModel) {
        await this.aiServiceManager.switchModel(model);
      }

      // Add file context if available
      if (context) {
        await this.aiServiceManager.addFileContext(context.fileName, context.content);
      }

      // Get AI response
      const response = await this.aiServiceManager.generateCode(userMessage, {
        filePath: context?.fileName || '',
        language: context?.language || 'plaintext',
        content: context?.content || ''
      });

      // Send response back to webview
      this.panel?.webview.postMessage({
        type: 'response',
        content: response
      });

    } catch (error) {
      logger.error('Failed to process message:', error as Error);
      this.sendErrorToWebview((error as any).message);
    }
  }

  private async handleClearChat(): Promise<void> {
    await this.aiServiceManager.clearContext();
    logger.info('Chat cleared');
  }

  private async handleAttachCurrentFile(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      this.sendErrorToWebview('No active editor found');
      return;
    }

    const document = editor.document;
    const context = {
      fileName: path.basename(document.fileName),
      language: document.languageId,
      content: document.getText()
    };

    // Add to AI context
    await this.aiServiceManager.addFileContext(document.fileName, document.getText());

    // Send context update to webview
    this.panel?.webview.postMessage({
      type: 'contextUpdate',
      context: context
    });

    logger.info(`File attached: ${context.fileName}`);
  }

  private async handleClearContext(): Promise<void> {
    await this.aiServiceManager.clearContext();
    
    this.panel?.webview.postMessage({
      type: 'contextUpdate',
      context: null
    });

    logger.info('Context cleared');
  }

  private async handleSwitchModel(model: string): Promise<void> {
    try {
      await this.aiServiceManager.switchModel(model);

      this.panel?.webview.postMessage({
        type: 'modelSwitched',
        model: model
      });

      logger.info(`Model switched to: ${model}`);
    } catch (error) {
      this.sendErrorToWebview(`Failed to switch model: ${(error as any).message}`);
    }
  }

  private async handleSwitchProvider(provider: string): Promise<void> {
    try {
      await this.aiServiceManager.switchProvider(provider as any);

      this.panel?.webview.postMessage({
        type: 'providerSwitched',
        provider: provider
      });

      logger.info(`Provider switched to: ${provider}`);
    } catch (error) {
      this.sendErrorToWebview(`Failed to switch provider: ${(error as any).message}`);
    }
  }

  private async handleCopyCode(code: string): Promise<void> {
    try {
      await vscode.env.clipboard.writeText(code);
      logger.info('Code copied to clipboard');
    } catch (error) {
      this.sendErrorToWebview('Failed to copy code to clipboard');
    }
  }

  private async handleApplyCode(code: string): Promise<void> {
    try {
      const editor = vscode.window.activeTextEditor;
      if (!editor) {
        this.sendErrorToWebview('No active editor found');
        return;
      }

      const selection = editor.selection;
      await editor.edit(editBuilder => {
        if (selection.isEmpty) {
          // Insert at cursor
          editBuilder.insert(selection.active, code);
        } else {
          // Replace selection
          editBuilder.replace(selection, code);
        }
      });

      logger.info('Code applied to editor');
    } catch (error) {
      this.sendErrorToWebview('Failed to apply code to editor');
    }
  }

  private async handleInsertCode(code: string): Promise<void> {
    try {
      const editor = vscode.window.activeTextEditor;
      if (!editor) {
        this.sendErrorToWebview('No active editor found');
        return;
      }

      const position = editor.selection.active;
      await editor.edit(editBuilder => {
        editBuilder.insert(position, code);
      });

      logger.info('Code inserted at cursor');
    } catch (error) {
      this.sendErrorToWebview('Failed to insert code');
    }
  }

  private sendErrorToWebview(error: string): void {
    this.panel?.webview.postMessage({
      type: 'error',
      error: error
    });
  }

  public async sendStreamingResponse(prompt: string): Promise<void> {
    if (!this.panel) return;

    try {
      await this.aiServiceManager.streamResponse(prompt, (chunk: string) => {
        this.panel?.webview.postMessage({
          type: 'streamChunk',
          chunk: chunk
        });
      });
    } catch (error) {
      this.sendErrorToWebview((error as any).message);
    }
  }

  public dispose(): void {
    if (this.panel) {
      this.panel.dispose();
      this.panel = undefined;
    }
  }
}
