{"version": "2.0.0", "tasks": [{"type": "npm", "script": "watch", "problemMatcher": "$tsc-watch", "isBackground": true, "presentation": {"reveal": "never"}, "group": {"kind": "build", "isDefault": true}}, {"type": "npm", "script": "compile", "problemMatcher": "$tsc", "group": "build", "label": "npm: compile"}, {"type": "npm", "script": "test", "problemMatcher": "$tsc", "group": "test", "label": "npm: test"}, {"type": "npm", "script": "lint", "problemMatcher": "$eslint-stylish", "group": "build", "label": "npm: lint"}, {"type": "npm", "script": "package", "group": "build", "label": "npm: package"}]}