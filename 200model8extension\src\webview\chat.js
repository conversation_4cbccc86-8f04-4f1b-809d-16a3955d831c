(function() {
    'use strict';

    // Get VS Code API
    const vscode = acquireVsCodeApi();

    // DOM elements
    const chatMessages = document.getElementById('chat-messages');
    const chatInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');
    const clearButton = document.getElementById('clear-button');
    const attachFileButton = document.getElementById('attach-file');
    const clearContextButton = document.getElementById('clear-context');
    const modelDropdown = document.getElementById('model-dropdown');
    const providerDropdown = document.getElementById('provider-dropdown');
    const contextInfo = document.getElementById('context-info');
    const typingIndicator = document.getElementById('typing-indicator');
    const quickActions = document.querySelectorAll('.quick-action');

    // State
    let conversationHistory = [];
    let currentContext = null;
    let isProcessing = false;

    // Initialize
    init();

    function init() {
        setupEventListeners();
        loadConversationHistory();
        focusInput();
    }

    function setupEventListeners() {
        // Send message
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Clear chat
        clearButton.addEventListener('click', clearChat);

        // Attach file
        attachFileButton.addEventListener('click', attachCurrentFile);

        // Clear context
        clearContextButton.addEventListener('click', clearContext);

        // Model selection
        modelDropdown.addEventListener('change', switchModel);

        // Provider selection
        providerDropdown.addEventListener('change', switchProvider);

        // Quick actions
        quickActions.forEach(action => {
            action.addEventListener('click', (e) => {
                const actionType = e.target.dataset.action;
                handleQuickAction(actionType);
            });
        });

        // Listen for messages from extension
        window.addEventListener('message', handleExtensionMessage);

        // Auto-resize textarea
        chatInput.addEventListener('input', autoResizeTextarea);
    }

    function sendMessage() {
        if (isProcessing) return;

        const message = chatInput.value.trim();
        if (!message) return;

        // Add user message to chat
        addMessage('user', message);
        
        // Clear input
        chatInput.value = '';
        autoResizeTextarea();

        // Show typing indicator
        showTypingIndicator();

        // Send to extension
        vscode.postMessage({
            type: 'sendMessage',
            message: message,
            context: currentContext,
            model: modelDropdown.value
        });

        isProcessing = true;
        updateButtonStates();
    }

    function addMessage(sender, content, timestamp = new Date()) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        
        const senderSpan = document.createElement('span');
        senderSpan.textContent = sender === 'user' ? '👤 You' : '🤖 Assistant';
        
        const timeSpan = document.createElement('span');
        timeSpan.textContent = timestamp.toLocaleTimeString();
        
        headerDiv.appendChild(senderSpan);
        headerDiv.appendChild(timeSpan);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        // Process content for code blocks
        contentDiv.innerHTML = processMessageContent(content);

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        chatMessages.appendChild(messageDiv);
        scrollToBottom();

        // Add to conversation history
        conversationHistory.push({
            sender,
            content,
            timestamp: timestamp.toISOString()
        });

        saveConversationHistory();
    }

    function processMessageContent(content) {
        // Convert markdown code blocks to HTML
        content = content.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, language, code) => {
            const lang = language || 'text';
            return `
                <div class="code-block">
                    <div class="code-header">
                        <span>${lang}</span>
                        <div class="code-actions">
                            <button class="code-action-btn" onclick="copyCode(this)">Copy</button>
                            <button class="code-action-btn" onclick="applyCode(this)">Apply</button>
                            <button class="code-action-btn" onclick="insertCode(this)">Insert</button>
                        </div>
                    </div>
                    <pre><code>${escapeHtml(code)}</code></pre>
                </div>
            `;
        });

        // Convert inline code
        content = content.replace(/`([^`]+)`/g, '<code style="background-color: var(--vscode-textCodeBlock-background); padding: 2px 4px; border-radius: 3px;">$1</code>');

        // Convert line breaks
        content = content.replace(/\n/g, '<br>');

        return content;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function showTypingIndicator() {
        typingIndicator.classList.add('visible');
        scrollToBottom();
    }

    function hideTypingIndicator() {
        typingIndicator.classList.remove('visible');
    }

    function clearChat() {
        const messages = chatMessages.querySelectorAll('.message:not(:first-child)');
        messages.forEach(msg => msg.remove());
        conversationHistory = [];
        saveConversationHistory();
        
        vscode.postMessage({
            type: 'clearChat'
        });
    }

    function attachCurrentFile() {
        vscode.postMessage({
            type: 'attachCurrentFile'
        });
    }

    function clearContext() {
        currentContext = null;
        contextInfo.classList.remove('visible');
        contextInfo.textContent = '';
        
        vscode.postMessage({
            type: 'clearContext'
        });
    }

    function switchModel() {
        const selectedModel = modelDropdown.value;
        vscode.postMessage({
            type: 'switchModel',
            model: selectedModel
        });
    }

    function switchProvider() {
        const selectedProvider = providerDropdown.value;
        vscode.postMessage({
            type: 'switchProvider',
            provider: selectedProvider
        });
    }

    function handleQuickAction(actionType) {
        const prompts = {
            explain: 'Please explain the selected code or current file',
            generate: 'Generate code for: ',
            refactor: 'Refactor the selected code to improve: ',
            test: 'Generate unit tests for the selected code',
            debug: 'Help me debug this code issue: ',
            optimize: 'Optimize the selected code for better performance',
            document: 'Generate documentation for the selected code'
        };

        const prompt = prompts[actionType];
        if (prompt) {
            if (actionType === 'generate' || actionType === 'refactor' || actionType === 'debug') {
                chatInput.value = prompt;
                chatInput.focus();
                chatInput.setSelectionRange(prompt.length, prompt.length);
            } else {
                chatInput.value = prompt;
                sendMessage();
            }
        }
    }

    function handleExtensionMessage(event) {
        const message = event.data;

        switch (message.type) {
            case 'response':
                hideTypingIndicator();
                addMessage('assistant', message.content);
                isProcessing = false;
                updateButtonStates();
                break;

            case 'error':
                hideTypingIndicator();
                showError(message.error);
                isProcessing = false;
                updateButtonStates();
                break;

            case 'contextUpdate':
                updateContext(message.context);
                break;

            case 'modelSwitched':
                showSuccess(`Switched to ${message.model}`);
                break;

            case 'streamChunk':
                handleStreamChunk(message.chunk);
                break;
        }
    }

    function updateContext(context) {
        currentContext = context;
        if (context) {
            contextInfo.textContent = `Context: ${context.fileName} (${context.language})`;
            contextInfo.classList.add('visible');
        } else {
            contextInfo.classList.remove('visible');
        }
    }

    function showError(error) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = `Error: ${error}`;
        chatMessages.appendChild(errorDiv);
        scrollToBottom();

        // Remove error message after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        chatMessages.appendChild(successDiv);
        scrollToBottom();

        // Remove success message after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    function handleStreamChunk(chunk) {
        // Handle streaming responses
        const lastMessage = chatMessages.querySelector('.message.assistant:last-child .message-content');
        if (lastMessage) {
            lastMessage.innerHTML += chunk;
            scrollToBottom();
        }
    }

    function updateButtonStates() {
        sendButton.disabled = isProcessing;
        sendButton.textContent = isProcessing ? 'Sending...' : 'Send';
        
        quickActions.forEach(action => {
            action.disabled = isProcessing;
        });
    }

    function autoResizeTextarea() {
        chatInput.style.height = 'auto';
        chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
    }

    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function focusInput() {
        chatInput.focus();
    }

    function saveConversationHistory() {
        vscode.setState({ conversationHistory });
    }

    function loadConversationHistory() {
        const state = vscode.getState();
        if (state && state.conversationHistory) {
            conversationHistory = state.conversationHistory;
            
            // Restore messages (skip the welcome message)
            conversationHistory.forEach(msg => {
                if (msg.sender !== 'system') {
                    addMessageToDOM(msg.sender, msg.content, new Date(msg.timestamp));
                }
            });
        }
    }

    function addMessageToDOM(sender, content, timestamp) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        
        const senderSpan = document.createElement('span');
        senderSpan.textContent = sender === 'user' ? '👤 You' : '🤖 Assistant';
        
        const timeSpan = document.createElement('span');
        timeSpan.textContent = timestamp.toLocaleTimeString();
        
        headerDiv.appendChild(senderSpan);
        headerDiv.appendChild(timeSpan);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processMessageContent(content);

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        chatMessages.appendChild(messageDiv);
    }

    // Global functions for code actions
    window.copyCode = function(button) {
        const codeBlock = button.closest('.code-block');
        const code = codeBlock.querySelector('code').textContent;
        
        vscode.postMessage({
            type: 'copyCode',
            code: code
        });
        
        button.textContent = 'Copied!';
        setTimeout(() => {
            button.textContent = 'Copy';
        }, 2000);
    };

    window.applyCode = function(button) {
        const codeBlock = button.closest('.code-block');
        const code = codeBlock.querySelector('code').textContent;
        
        vscode.postMessage({
            type: 'applyCode',
            code: code
        });
        
        showSuccess('Code applied to editor');
    };

    window.insertCode = function(button) {
        const codeBlock = button.closest('.code-block');
        const code = codeBlock.querySelector('code').textContent;
        
        vscode.postMessage({
            type: 'insertCode',
            code: code
        });
        
        showSuccess('Code inserted at cursor');
    };

})();
