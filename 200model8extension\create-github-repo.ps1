# Create GitHub Repository via API
param(
    [string]$Token = "****************************************",
    [string]$RepoName = "200Model8Extension",
    [string]$Description = "AI-powered VS Code extension with dual provider support for comprehensive code generation and assistance"
)

Write-Host "🚀 Creating GitHub repository: $RepoName" -ForegroundColor Green

# Repository data
$repoData = @{
    name = $RepoName
    description = $Description
    private = $false
    has_issues = $true
    has_projects = $true
    has_wiki = $true
    auto_init = $false
} | ConvertTo-Json

# Headers for GitHub API
$headers = @{
    "Authorization" = "token $Token"
    "Accept" = "application/vnd.github.v3+json"
    "User-Agent" = "PowerShell-Script"
}

try {
    # Create repository
    $response = Invoke-RestMethod -Uri "https://api.github.com/user/repos" -Method Post -Headers $headers -Body $repoData -ContentType "application/json"
    
    Write-Host "✅ Repository created successfully!" -ForegroundColor Green
    Write-Host "📍 Repository URL: $($response.html_url)" -ForegroundColor Blue
    Write-Host "🔗 Clone URL: $($response.clone_url)" -ForegroundColor Blue
    
    # Now push the code
    Write-Host ""
    Write-Host "📤 Pushing code to GitHub..." -ForegroundColor Yellow
    
    git push -u origin main
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Code pushed successfully!" -ForegroundColor Green
        Write-Host "🎉 Your repository is ready at: $($response.html_url)" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to push code. Please try manually." -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Failed to create repository: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Message -like "*already exists*") {
        Write-Host "💡 Repository might already exist. Trying to push..." -ForegroundColor Yellow
        git push -u origin main
    } else {
        Write-Host "💡 Please create the repository manually at: https://github.com/new" -ForegroundColor Yellow
        Write-Host "   Repository name: $RepoName" -ForegroundColor White
        Write-Host "   Description: $Description" -ForegroundColor White
    }
}
