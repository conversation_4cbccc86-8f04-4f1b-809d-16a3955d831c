import * as vscode from 'vscode';
import { logger } from './logger';

export interface ParsedCode {
  functions: FunctionInfo[];
  classes: ClassInfo[];
  imports: ImportInfo[];
  exports: ExportInfo[];
  variables: VariableInfo[];
  comments: CommentInfo[];
}

export interface FunctionInfo {
  name: string;
  parameters: ParameterInfo[];
  returnType?: string;
  startLine: number;
  endLine: number;
  isAsync: boolean;
  isExported: boolean;
  documentation?: string;
}

export interface ClassInfo {
  name: string;
  methods: FunctionInfo[];
  properties: PropertyInfo[];
  extends?: string;
  implements?: string[];
  startLine: number;
  endLine: number;
  isExported: boolean;
  documentation?: string;
}

export interface ImportInfo {
  module: string;
  imports: string[];
  isDefault: boolean;
  line: number;
}

export interface ExportInfo {
  name: string;
  type: 'function' | 'class' | 'variable' | 'default';
  line: number;
}

export interface VariableInfo {
  name: string;
  type?: string;
  isConst: boolean;
  line: number;
  isExported: boolean;
}

export interface PropertyInfo {
  name: string;
  type?: string;
  isPrivate: boolean;
  isStatic: boolean;
  line: number;
}

export interface ParameterInfo {
  name: string;
  type?: string;
  isOptional: boolean;
  defaultValue?: string;
}

export interface CommentInfo {
  content: string;
  type: 'single' | 'multi' | 'jsdoc';
  line: number;
  endLine?: number;
}

export class CodeParser {
  public static parseCode(code: string, language: string): ParsedCode {
    const lines = code.split('\n');
    
    switch (language) {
      case 'typescript':
      case 'javascript':
        return this.parseJavaScript(lines);
      case 'python':
        return this.parsePython(lines);
      case 'java':
        return this.parseJava(lines);
      case 'csharp':
        return this.parseCSharp(lines);
      default:
        return this.parseGeneric(lines);
    }
  }

  private static parseJavaScript(lines: string[]): ParsedCode {
    const result: ParsedCode = {
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      comments: []
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Parse imports
      if (line.startsWith('import ')) {
        const importInfo = this.parseImport(line, i);
        if (importInfo) {
          result.imports.push(importInfo);
        }
      }
      
      // Parse exports
      if (line.startsWith('export ')) {
        const exportInfo = this.parseExport(line, i);
        if (exportInfo) {
          result.exports.push(exportInfo);
        }
      }
      
      // Parse functions
      if (this.isFunctionDeclaration(line)) {
        const functionInfo = this.parseFunction(lines, i);
        if (functionInfo) {
          result.functions.push(functionInfo);
        }
      }
      
      // Parse classes
      if (line.startsWith('class ')) {
        const classInfo = this.parseClass(lines, i);
        if (classInfo) {
          result.classes.push(classInfo);
        }
      }
      
      // Parse variables
      if (this.isVariableDeclaration(line)) {
        const variableInfo = this.parseVariable(line, i);
        if (variableInfo) {
          result.variables.push(variableInfo);
        }
      }
      
      // Parse comments
      if (this.isComment(line)) {
        const commentInfo = this.parseComment(lines, i);
        if (commentInfo) {
          result.comments.push(commentInfo);
        }
      }
    }

    return result;
  }

  private static parsePython(lines: string[]): ParsedCode {
    const result: ParsedCode = {
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      comments: []
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Parse imports
      if (line.startsWith('import ') || line.startsWith('from ')) {
        const importInfo = this.parsePythonImport(line, i);
        if (importInfo) {
          result.imports.push(importInfo);
        }
      }
      
      // Parse functions
      if (line.startsWith('def ')) {
        const functionInfo = this.parsePythonFunction(lines, i);
        if (functionInfo) {
          result.functions.push(functionInfo);
        }
      }
      
      // Parse classes
      if (line.startsWith('class ')) {
        const classInfo = this.parsePythonClass(lines, i);
        if (classInfo) {
          result.classes.push(classInfo);
        }
      }
      
      // Parse comments
      if (line.startsWith('#')) {
        result.comments.push({
          content: line.substring(1).trim(),
          type: 'single',
          line: i + 1
        });
      }
    }

    return result;
  }

  private static parseGeneric(lines: string[]): ParsedCode {
    return {
      functions: [],
      classes: [],
      imports: [],
      exports: [],
      variables: [],
      comments: lines.map((line, index) => ({
        content: line,
        type: 'single' as const,
        line: index + 1
      })).filter(comment => comment.content.trim().startsWith('//') || comment.content.trim().startsWith('#'))
    };
  }

  private static parseImport(line: string, lineNumber: number): ImportInfo | null {
    const importMatch = line.match(/import\s+(.+)\s+from\s+['"](.+)['"]/);
    if (importMatch) {
      const imports = importMatch[1].split(',').map(imp => imp.trim().replace(/[{}]/g, ''));
      return {
        module: importMatch[2],
        imports,
        isDefault: !importMatch[1].includes('{'),
        line: lineNumber + 1
      };
    }
    return null;
  }

  private static parseExport(line: string, lineNumber: number): ExportInfo | null {
    if (line.includes('export default')) {
      return {
        name: 'default',
        type: 'default',
        line: lineNumber + 1
      };
    }
    
    const exportMatch = line.match(/export\s+(function|class|const|let|var)\s+(\w+)/);
    if (exportMatch) {
      return {
        name: exportMatch[2],
        type: exportMatch[1] as 'function' | 'class' | 'variable',
        line: lineNumber + 1
      };
    }
    
    return null;
  }

  private static isFunctionDeclaration(line: string): boolean {
    return /^(async\s+)?function\s+\w+/.test(line) || 
           /^(const|let|var)\s+\w+\s*=\s*(async\s+)?\(/.test(line) ||
           /^\w+\s*:\s*(async\s+)?\(/.test(line);
  }

  private static parseFunction(lines: string[], startIndex: number): FunctionInfo | null {
    const line = lines[startIndex].trim();
    const nameMatch = line.match(/(?:function\s+)?(\w+)/);
    
    if (!nameMatch) return null;
    
    const name = nameMatch[1];
    const isAsync = line.includes('async');
    const isExported = line.includes('export');
    
    // Find function end (simplified)
    let endLine = startIndex;
    let braceCount = 0;
    for (let i = startIndex; i < lines.length; i++) {
      const currentLine = lines[i];
      braceCount += (currentLine.match(/{/g) || []).length;
      braceCount -= (currentLine.match(/}/g) || []).length;
      if (braceCount === 0 && i > startIndex) {
        endLine = i;
        break;
      }
    }
    
    return {
      name,
      parameters: [], // Simplified - would need more complex parsing
      startLine: startIndex + 1,
      endLine: endLine + 1,
      isAsync,
      isExported
    };
  }

  private static parseClass(lines: string[], startIndex: number): ClassInfo | null {
    const line = lines[startIndex].trim();
    const nameMatch = line.match(/class\s+(\w+)/);
    
    if (!nameMatch) return null;
    
    const name = nameMatch[1];
    const isExported = line.includes('export');
    
    // Find class end (simplified)
    let endLine = startIndex;
    let braceCount = 0;
    for (let i = startIndex; i < lines.length; i++) {
      const currentLine = lines[i];
      braceCount += (currentLine.match(/{/g) || []).length;
      braceCount -= (currentLine.match(/}/g) || []).length;
      if (braceCount === 0 && i > startIndex) {
        endLine = i;
        break;
      }
    }
    
    return {
      name,
      methods: [],
      properties: [],
      startLine: startIndex + 1,
      endLine: endLine + 1,
      isExported
    };
  }

  private static isVariableDeclaration(line: string): boolean {
    return /^(const|let|var)\s+\w+/.test(line);
  }

  private static parseVariable(line: string, lineNumber: number): VariableInfo | null {
    const match = line.match(/^(const|let|var)\s+(\w+)/);
    if (match) {
      return {
        name: match[2],
        isConst: match[1] === 'const',
        line: lineNumber + 1,
        isExported: line.includes('export')
      };
    }
    return null;
  }

  private static isComment(line: string): boolean {
    return line.startsWith('//') || line.startsWith('/*') || line.startsWith('*');
  }

  private static parseComment(lines: string[], startIndex: number): CommentInfo | null {
    const line = lines[startIndex].trim();
    
    if (line.startsWith('//')) {
      return {
        content: line.substring(2).trim(),
        type: 'single',
        line: startIndex + 1
      };
    }
    
    if (line.startsWith('/*')) {
      let endLine = startIndex;
      let content = line.substring(2);
      
      if (!line.includes('*/')) {
        for (let i = startIndex + 1; i < lines.length; i++) {
          const currentLine = lines[i].trim();
          content += '\n' + currentLine;
          if (currentLine.includes('*/')) {
            endLine = i;
            break;
          }
        }
      }
      
      content = content.replace('*/', '').trim();
      
      return {
        content,
        type: line.includes('/**') ? 'jsdoc' : 'multi',
        line: startIndex + 1,
        endLine: endLine + 1
      };
    }
    
    return null;
  }

  private static parsePythonImport(line: string, lineNumber: number): ImportInfo | null {
    if (line.startsWith('import ')) {
      const module = line.substring(7).trim();
      return {
        module,
        imports: [module],
        isDefault: true,
        line: lineNumber + 1
      };
    }
    
    if (line.startsWith('from ')) {
      const match = line.match(/from\s+(\S+)\s+import\s+(.+)/);
      if (match) {
        const imports = match[2].split(',').map(imp => imp.trim());
        return {
          module: match[1],
          imports,
          isDefault: false,
          line: lineNumber + 1
        };
      }
    }
    
    return null;
  }

  private static parsePythonFunction(lines: string[], startIndex: number): FunctionInfo | null {
    const line = lines[startIndex].trim();
    const match = line.match(/def\s+(\w+)\s*\(/);
    
    if (!match) return null;
    
    const name = match[1];
    const isAsync = line.includes('async def');
    
    // Find function end by indentation
    let endLine = startIndex;
    const baseIndent = lines[startIndex].length - lines[startIndex].trimLeft().length;
    
    for (let i = startIndex + 1; i < lines.length; i++) {
      const currentLine = lines[i];
      if (currentLine.trim() === '') continue;
      
      const currentIndent = currentLine.length - currentLine.trimLeft().length;
      if (currentIndent <= baseIndent) {
        endLine = i - 1;
        break;
      }
    }
    
    return {
      name,
      parameters: [],
      startLine: startIndex + 1,
      endLine: endLine + 1,
      isAsync,
      isExported: false // Python doesn't have explicit exports
    };
  }

  private static parsePythonClass(lines: string[], startIndex: number): ClassInfo | null {
    const line = lines[startIndex].trim();
    const match = line.match(/class\s+(\w+)/);
    
    if (!match) return null;
    
    const name = match[1];
    
    // Find class end by indentation
    let endLine = startIndex;
    const baseIndent = lines[startIndex].length - lines[startIndex].trimLeft().length;
    
    for (let i = startIndex + 1; i < lines.length; i++) {
      const currentLine = lines[i];
      if (currentLine.trim() === '') continue;
      
      const currentIndent = currentLine.length - currentLine.trimLeft().length;
      if (currentIndent <= baseIndent) {
        endLine = i - 1;
        break;
      }
    }
    
    return {
      name,
      methods: [],
      properties: [],
      startLine: startIndex + 1,
      endLine: endLine + 1,
      isExported: false
    };
  }

  private static parseJava(lines: string[]): ParsedCode {
    // Simplified Java parsing - would need more comprehensive implementation
    return this.parseGeneric(lines);
  }

  private static parseCSharp(lines: string[]): ParsedCode {
    // Simplified C# parsing - would need more comprehensive implementation
    return this.parseGeneric(lines);
  }

  public static getLanguageFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cs': 'csharp',
      'cpp': 'cpp',
      'c': 'c',
      'h': 'c',
      'hpp': 'cpp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sql': 'sql'
    };
    
    return languageMap[extension || ''] || 'plaintext';
  }

  public static extractContext(code: string, position: vscode.Position, contextLines: number = 10): string {
    const lines = code.split('\n');
    const startLine = Math.max(0, position.line - contextLines);
    const endLine = Math.min(lines.length - 1, position.line + contextLines);
    
    return lines.slice(startLine, endLine + 1).join('\n');
  }
}
