<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>200Model8 AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        #chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        #chat-header {
            background-color: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 12px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        #chat-header h3 {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
        }

        #model-selector select {
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
        }

        #chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            display: flex;
            flex-direction: column;
            max-width: 85%;
            word-wrap: break-word;
        }

        .message.user {
            align-self: flex-end;
        }

        .message.assistant {
            align-self: flex-start;
        }

        .message-header {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .message-content {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 8px;
            padding: 12px;
            line-height: 1.5;
        }

        .message.user .message-content {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        .message.assistant .message-content {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
        }

        .code-block {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            position: relative;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
        }

        .code-actions {
            display: flex;
            gap: 8px;
        }

        .code-action-btn {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .code-action-btn:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        #chat-input-container {
            border-top: 1px solid var(--vscode-panel-border);
            padding: 16px;
            flex-shrink: 0;
        }

        #context-info {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 8px;
            display: none;
        }

        #context-info.visible {
            display: block;
        }

        #chat-input {
            width: 100%;
            min-height: 60px;
            max-height: 120px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 6px;
            padding: 12px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            outline: none;
        }

        #chat-input:focus {
            border-color: var(--vscode-focusBorder);
        }

        #chat-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
        }

        .action-group {
            display: flex;
            gap: 8px;
        }

        .btn {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 13px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .btn.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .btn.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        #quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
        }

        .quick-action {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            border: none;
            border-radius: 12px;
            padding: 6px 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: var(--vscode-descriptionForeground);
            font-size: 12px;
            font-style: italic;
            margin: 8px 0;
        }

        .typing-indicator.visible {
            display: flex;
        }

        .typing-dots {
            display: flex;
            gap: 2px;
        }

        .typing-dot {
            width: 4px;
            height: 4px;
            background-color: var(--vscode-descriptionForeground);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .error-message {
            background-color: var(--vscode-inputValidation-errorBackground);
            color: var(--vscode-inputValidation-errorForeground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            border-radius: 4px;
            padding: 8px 12px;
            margin: 8px 0;
            font-size: 12px;
        }

        .success-message {
            background-color: var(--vscode-terminal-ansiGreen);
            color: var(--vscode-editor-background);
            border-radius: 4px;
            padding: 8px 12px;
            margin: 8px 0;
            font-size: 12px;
        }

        /* Scrollbar styling */
        #chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        #chat-messages::-webkit-scrollbar-track {
            background: var(--vscode-scrollbarSlider-background);
        }

        #chat-messages::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-hoverBackground);
            border-radius: 4px;
        }

        #chat-messages::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-activeBackground);
        }

        /* Loading animation */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--vscode-descriptionForeground);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="chat-container">
        <div id="chat-header">
            <h3>200Model8 AI Assistant</h3>
            <div id="model-selector">
                <select id="model-dropdown">
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="claude-3-opus">Claude 3 Opus</option>
                    <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                    <option value="gemini-pro">Gemini Pro</option>
                </select>
            </div>
        </div>
        
        <div id="chat-messages">
            <div class="message assistant">
                <div class="message-header">
                    <span>🤖 Assistant</span>
                    <span id="welcome-time"></span>
                </div>
                <div class="message-content">
                    Welcome to 200Model8! I'm your AI coding assistant. I can help you:
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Generate code in any programming language</li>
                        <li>Explain existing code</li>
                        <li>Refactor and optimize code</li>
                        <li>Create entire project structures</li>
                        <li>Debug issues and suggest fixes</li>
                        <li>Generate tests and documentation</li>
                    </ul>
                    How can I help you today?
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typing-indicator">
            <span>AI is thinking</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div id="chat-input-container">
            <div id="context-info"></div>
            <textarea id="chat-input" placeholder="Ask me anything about your code..." rows="3"></textarea>
            <div id="chat-actions">
                <div class="action-group">
                    <button id="attach-file" class="btn secondary" title="Attach current file">📎 Attach File</button>
                    <button id="clear-context" class="btn secondary" title="Clear conversation context">🗑️ Clear Context</button>
                </div>
                <div class="action-group">
                    <button id="clear-button" class="btn secondary">Clear Chat</button>
                    <button id="send-button" class="btn">Send</button>
                </div>
            </div>
            
            <div id="quick-actions">
                <button class="quick-action" data-action="explain">💡 Explain Code</button>
                <button class="quick-action" data-action="generate">⚡ Generate Code</button>
                <button class="quick-action" data-action="refactor">🔧 Refactor</button>
                <button class="quick-action" data-action="test">🧪 Generate Tests</button>
                <button class="quick-action" data-action="debug">🐛 Debug</button>
                <button class="quick-action" data-action="optimize">🚀 Optimize</button>
                <button class="quick-action" data-action="document">📝 Document</button>
            </div>
        </div>
    </div>

    <script>
        // Initialize welcome time
        document.getElementById('welcome-time').textContent = new Date().toLocaleTimeString();
    </script>
</body>
</html>
