import * as vscode from 'vscode';
import { logger } from './logger';

export class CodeFormatter {
  public static async formatCode(code: string, language: string): Promise<string> {
    try {
      // Create a temporary document to use VS Code's formatting
      const document = await vscode.workspace.openTextDocument({
        content: code,
        language: language
      });

      // Get formatting edits
      const edits = await vscode.commands.executeCommand<vscode.TextEdit[]>(
        'vscode.executeFormatDocumentProvider',
        document.uri
      );

      if (edits && edits.length > 0) {
        // Apply formatting edits
        let formattedCode = code;
        for (const edit of edits.reverse()) { // Reverse to apply from end to start
          const startOffset = document.offsetAt(edit.range.start);
          const endOffset = document.offsetAt(edit.range.end);
          formattedCode = formattedCode.substring(0, startOffset) + 
                         edit.newText + 
                         formattedCode.substring(endOffset);
        }
        return formattedCode;
      }

      return code;

    } catch (error) {
      logger.error('Failed to format code:', error as Error);
      return code; // Return original code if formatting fails
    }
  }

  public static formatForLanguage(code: string, language: string): string {
    switch (language) {
      case 'javascript':
      case 'typescript':
        return this.formatJavaScript(code);
      case 'python':
        return this.formatPython(code);
      case 'java':
        return this.formatJava(code);
      case 'json':
        return this.formatJSON(code);
      default:
        return code;
    }
  }

  private static formatJavaScript(code: string): string {
    try {
      // Basic JavaScript formatting
      return code
        .replace(/;\s*\n/g, ';\n')
        .replace(/{\s*\n/g, '{\n')
        .replace(/}\s*\n/g, '}\n')
        .replace(/,\s*\n/g, ',\n');
    } catch {
      return code;
    }
  }

  private static formatPython(code: string): string {
    try {
      // Basic Python formatting
      const lines = code.split('\n');
      let indentLevel = 0;
      const formattedLines: string[] = [];

      for (const line of lines) {
        const trimmedLine = line.trim();
        
        if (trimmedLine.endsWith(':')) {
          formattedLines.push('    '.repeat(indentLevel) + trimmedLine);
          indentLevel++;
        } else if (trimmedLine === '' || trimmedLine.startsWith('#')) {
          formattedLines.push(trimmedLine);
        } else {
          if (trimmedLine.startsWith('except') || 
              trimmedLine.startsWith('elif') || 
              trimmedLine.startsWith('else') ||
              trimmedLine.startsWith('finally')) {
            indentLevel = Math.max(0, indentLevel - 1);
          }
          
          formattedLines.push('    '.repeat(indentLevel) + trimmedLine);
          
          if (line.includes('def ') || line.includes('class ') || 
              line.includes('if ') || line.includes('for ') || 
              line.includes('while ') || line.includes('with ') ||
              line.includes('try:')) {
            // These were already handled above
          }
        }
      }

      return formattedLines.join('\n');
    } catch {
      return code;
    }
  }

  private static formatJava(code: string): string {
    try {
      // Basic Java formatting
      return code
        .replace(/{\s*\n/g, ' {\n')
        .replace(/}\s*\n/g, '}\n')
        .replace(/;\s*\n/g, ';\n');
    } catch {
      return code;
    }
  }

  private static formatJSON(code: string): string {
    try {
      const parsed = JSON.parse(code);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return code;
    }
  }

  public static addIndentation(code: string, indentSize: number = 2): string {
    const indent = ' '.repeat(indentSize);
    return code.split('\n').map(line => {
      if (line.trim() === '') return line;
      return indent + line;
    }).join('\n');
  }

  public static removeIndentation(code: string): string {
    const lines = code.split('\n');
    if (lines.length === 0) return code;

    // Find minimum indentation
    let minIndent = Infinity;
    for (const line of lines) {
      if (line.trim() === '') continue;
      const indent = line.length - line.trimLeft().length;
      minIndent = Math.min(minIndent, indent);
    }

    if (minIndent === Infinity || minIndent === 0) return code;

    // Remove minimum indentation from all lines
    return lines.map(line => {
      if (line.trim() === '') return line;
      return line.substring(minIndent);
    }).join('\n');
  }

  public static normalizeLineEndings(code: string): string {
    return code.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
  }

  public static trimEmptyLines(code: string): string {
    return code.replace(/^\s*\n+/, '').replace(/\n+\s*$/, '');
  }
}
