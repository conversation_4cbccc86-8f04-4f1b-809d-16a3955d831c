import * as vscode from 'vscode';
import { CodeService } from '../services/codeService';
import { logger } from '../utils/logger';
import { configManager } from '../utils/config';

export class CodeActionProvider implements vscode.CodeActionProvider {
  private codeService: CodeService;

  constructor() {
    this.codeService = CodeService.getInstance();
  }

  public async provideCodeActions(
    document: vscode.TextDocument,
    range: vscode.Range | vscode.Selection,
    context: vscode.CodeActionContext,
    token: vscode.CancellationToken
  ): Promise<vscode.CodeAction[]> {
    try {
      const config = configManager.getConfig();
      if (!config.puterApiKey) {
        return [];
      }

      const actions: vscode.CodeAction[] = [];
      const selectedText = document.getText(range);
      const hasSelection = !range.isEmpty && selectedText.trim().length > 0;

      // AI-powered quick fixes for diagnostics
      if (context.diagnostics && context.diagnostics.length > 0) {
        for (const diagnostic of context.diagnostics) {
          const quickFix = await this.createQuickFix(document, range, diagnostic);
          if (quickFix) {
            actions.push(quickFix);
          }
        }
      }

      // Refactoring actions
      if (hasSelection) {
        actions.push(...this.createRefactoringActions(document, range, selectedText));
      }

      // Code generation actions
      actions.push(...this.createGenerationActions(document, range));

      // Optimization actions
      if (hasSelection) {
        actions.push(...this.createOptimizationActions(document, range, selectedText));
      }

      // Documentation actions
      if (hasSelection) {
        actions.push(...this.createDocumentationActions(document, range, selectedText));
      }

      logger.debug(`Provided ${actions.length} code actions`);
      return actions;

    } catch (error) {
      logger.error('Failed to provide code actions:', error as Error);
      return [];
    }
  }

  private async createQuickFix(
    document: vscode.TextDocument,
    range: vscode.Range,
    diagnostic: vscode.Diagnostic
  ): Promise<vscode.CodeAction | undefined> {
    try {
      const action = new vscode.CodeAction(
        `🤖 AI Fix: ${diagnostic.message}`,
        vscode.CodeActionKind.QuickFix
      );

      action.diagnostics = [diagnostic];
      action.isPreferred = true;

      // Get the problematic code
      const problemCode = document.getText(diagnostic.range);
      const contextRange = new vscode.Range(
        Math.max(0, diagnostic.range.start.line - 5),
        0,
        Math.min(document.lineCount - 1, diagnostic.range.end.line + 5),
        0
      );
      const context = document.getText(contextRange);

      // Create the fix command
      action.command = {
        command: '200model8.applyAIFix',
        title: 'Apply AI Fix',
        arguments: [
          document.uri,
          diagnostic.range,
          problemCode,
          context,
          diagnostic.message,
          document.languageId
        ]
      };

      return action;

    } catch (error) {
      logger.error('Failed to create quick fix:', error as Error);
      return undefined;
    }
  }

  private createRefactoringActions(
    document: vscode.TextDocument,
    range: vscode.Range,
    selectedText: string
  ): vscode.CodeAction[] {
    const actions: vscode.CodeAction[] = [];

    // Extract method
    const extractMethod = new vscode.CodeAction(
      '🤖 Extract Method',
      vscode.CodeActionKind.RefactorExtract
    );
    extractMethod.command = {
      command: '200model8.extractMethod',
      title: 'Extract Method',
      arguments: [document.uri, range, selectedText]
    };
    actions.push(extractMethod);

    // Refactor for readability
    const refactorReadability = new vscode.CodeAction(
      '🤖 Improve Readability',
      vscode.CodeActionKind.RefactorRewrite
    );
    refactorReadability.command = {
      command: '200model8.refactorForReadability',
      title: 'Improve Readability',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(refactorReadability);

    // Convert to modern syntax
    const modernize = new vscode.CodeAction(
      '🤖 Modernize Code',
      vscode.CodeActionKind.RefactorRewrite
    );
    modernize.command = {
      command: '200model8.modernizeCode',
      title: 'Modernize Code',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(modernize);

    return actions;
  }

  private createGenerationActions(
    document: vscode.TextDocument,
    range: vscode.Range
  ): vscode.CodeAction[] {
    const actions: vscode.CodeAction[] = [];

    // Generate function
    const generateFunction = new vscode.CodeAction(
      '🤖 Generate Function',
      vscode.CodeActionKind.Source
    );
    generateFunction.command = {
      command: '200model8.generateFunctionAtCursor',
      title: 'Generate Function',
      arguments: [document.uri, range.start, document.languageId]
    };
    actions.push(generateFunction);

    // Generate class
    const generateClass = new vscode.CodeAction(
      '🤖 Generate Class',
      vscode.CodeActionKind.Source
    );
    generateClass.command = {
      command: '200model8.generateClassAtCursor',
      title: 'Generate Class',
      arguments: [document.uri, range.start, document.languageId]
    };
    actions.push(generateClass);

    // Generate tests
    const generateTests = new vscode.CodeAction(
      '🤖 Generate Tests for File',
      vscode.CodeActionKind.Source
    );
    generateTests.command = {
      command: '200model8.generateTests',
      title: 'Generate Tests',
      arguments: []
    };
    actions.push(generateTests);

    return actions;
  }

  private createOptimizationActions(
    document: vscode.TextDocument,
    range: vscode.Range,
    selectedText: string
  ): vscode.CodeAction[] {
    const actions: vscode.CodeAction[] = [];

    // Optimize performance
    const optimizePerformance = new vscode.CodeAction(
      '🤖 Optimize Performance',
      vscode.CodeActionKind.RefactorRewrite
    );
    optimizePerformance.command = {
      command: '200model8.optimizePerformance',
      title: 'Optimize Performance',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(optimizePerformance);

    // Reduce complexity
    const reduceComplexity = new vscode.CodeAction(
      '🤖 Reduce Complexity',
      vscode.CodeActionKind.RefactorRewrite
    );
    reduceComplexity.command = {
      command: '200model8.reduceComplexity',
      title: 'Reduce Complexity',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(reduceComplexity);

    return actions;
  }

  private createDocumentationActions(
    document: vscode.TextDocument,
    range: vscode.Range,
    selectedText: string
  ): vscode.CodeAction[] {
    const actions: vscode.CodeAction[] = [];

    // Add documentation
    const addDocumentation = new vscode.CodeAction(
      '🤖 Add Documentation',
      vscode.CodeActionKind.Source
    );
    addDocumentation.command = {
      command: '200model8.addDocumentation',
      title: 'Add Documentation',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(addDocumentation);

    // Add inline comments
    const addComments = new vscode.CodeAction(
      '🤖 Add Comments',
      vscode.CodeActionKind.Source
    );
    addComments.command = {
      command: '200model8.addComments',
      title: 'Add Comments',
      arguments: [document.uri, range, selectedText, document.languageId]
    };
    actions.push(addComments);

    return actions;
  }
}
