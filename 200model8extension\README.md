# 200Model8 - AI Development Assistant

A powerful VS Code extension that provides comprehensive AI-powered development assistance with full file editing capabilities, code generation, and project management using Puter.js for AI model access.

## 🚀 Features

### Core Capabilities
- **Full File System Access**: Create, read, write, delete, and modify files across your entire workspace
- **Code Generation**: Generate complete files, functions, classes, and code snippets in any programming language
- **Code Editing**: Modify existing code with precise insertions, deletions, and replacements
- **Project Management**: Create new projects, add dependencies, and configure settings
- **Multi-File Operations**: Work across multiple files simultaneously
- **Real-time AI Chat**: Interactive chat interface with streaming responses

### AI-Powered Features
- **Code Explanation**: Get detailed explanations of any code snippet
- **Code Refactoring**: Improve code quality, readability, and performance
- **Bug Detection**: Find and fix issues in your code
- **Test Generation**: Create comprehensive unit tests automatically
- **Documentation**: Generate comments and documentation
- **Code Optimization**: Improve performance and reduce complexity

### VS Code Integration
- **IntelliSense**: AI-enhanced autocomplete and suggestions
- **Hover Information**: Get AI explanations on hover
- **Code Actions**: Quick fixes and refactoring suggestions
- **Go to Definition**: Enhanced symbol navigation
- **Multi-language Support**: Works with JavaScript, TypeScript, Python, Java, C#, Go, Rust, and more

## 📦 Installation

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "200Model8"
4. Click Install
5. Reload VS Code

## ⚙️ Configuration

### Required Setup
1. Get a Puter.js API key from [Puter.com](https://puter.com)
2. Open VS Code Settings (Ctrl+,)
3. Search for "200model8"
4. Enter your API key in the "Puter Api Key" field

### Available Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `200model8.puterApiKey` | Your Puter.js API key | "" |
| `200model8.defaultModel` | Default AI model to use | "gpt-4" |
| `200model8.autoSave` | Automatically save generated files | true |
| `200model8.contextLines` | Number of context lines for AI requests | 100 |
| `200model8.enableLogging` | Enable detailed logging | false |
| `200model8.maxTokens` | Maximum tokens for AI responses | 4000 |

## 🎯 Usage

### Quick Start
1. Press `Ctrl+Alt+8` to open the AI chat
2. Select code and right-click for AI actions
3. Use the Command Palette (`Ctrl+Shift+P`) and search for "200Model8"

### Main Commands

| Command | Shortcut | Description |
|---------|----------|-------------|
| Open AI Chat | `Ctrl+Alt+8` | Open the interactive AI chat interface |
| Generate Code | `Ctrl+Alt+G` | Generate code from natural language description |
| Explain Code | `Ctrl+Alt+E` | Explain selected code (requires selection) |
| Refactor Code | `Ctrl+Alt+R` | Refactor selected code (requires selection) |
| Create File | - | Create a new file with AI-generated content |
| Edit File | - | Modify existing files with AI assistance |
| Generate Project | - | Create complete project structures |
| Generate Tests | - | Create unit tests for your code |
| Debug Code | - | Get help debugging code issues |

### Chat Interface Features
- **Real-time AI conversation** with syntax highlighting
- **Code application** - Apply generated code directly to your files
- **File attachment** - Include current file context in conversations
- **Model switching** - Change AI models on the fly
- **Quick actions** - One-click access to common tasks
- **Conversation history** - Persistent chat history

### Code Actions
Right-click on any code to access AI-powered actions:
- **AI Fix** - Automatically fix detected issues
- **Extract Method** - Extract code into separate methods
- **Improve Readability** - Make code more readable
- **Modernize Code** - Update to modern syntax
- **Optimize Performance** - Improve code performance
- **Add Documentation** - Generate comprehensive documentation
- **Add Comments** - Add inline explanations

## 🛠️ Supported Languages

- JavaScript/TypeScript
- Python
- Java
- C#
- C/C++
- Go
- Rust
- PHP
- Ruby
- HTML/CSS
- JSON/YAML
- Markdown
- And many more...

## 📋 Examples

### Generate a Function
1. Open the AI chat (`Ctrl+Alt+8`)
2. Type: "Generate a function that validates email addresses"
3. The AI will create a complete function with error handling and tests

### Refactor Code
1. Select code you want to improve
2. Right-click and choose "Refactor Code"
3. Describe how you want to refactor it
4. Review and apply the changes

### Create a Project
1. Use Command Palette: "200Model8: Generate Project"
2. Describe your project (e.g., "A React todo app with authentication")
3. Choose language, framework, and features
4. The extension creates the complete project structure

### Debug Issues
1. Select problematic code
2. Use "Debug Code with AI" command
3. Describe the error you're experiencing
4. Get detailed analysis and solutions

## 🔧 Advanced Features

### Streaming Responses
The extension supports real-time streaming of AI responses for faster interaction.

### Context Management
- Automatically includes file context in AI requests
- Maintains conversation history
- Supports multi-file context

### Batch Operations
- Create multiple files at once
- Update multiple files simultaneously
- Perform project-wide refactoring

### Error Handling
- Comprehensive error recovery
- Graceful handling of API failures
- User-friendly error messages

## 🚨 Troubleshooting

### Common Issues

**Extension not working?**
- Check that your Puter API key is configured correctly
- Ensure you have an active internet connection
- Check the extension logs: Command Palette → "200Model8: Show Logs"

**AI responses are slow?**
- Try switching to a faster model (GPT-3.5 Turbo)
- Reduce the context lines setting
- Check your internet connection

**Code actions not appearing?**
- Make sure you have text selected
- Check that the file language is supported
- Restart VS Code if needed

### Getting Help
- Check the extension logs for detailed error information
- Report issues on our GitHub repository
- Join our community Discord for support

## 🔒 Privacy & Security

- Your code is sent to Puter.js AI models for processing
- No code is stored permanently by the extension
- API keys are stored securely in VS Code settings
- All communication is encrypted

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for more information.

## 📞 Support

- GitHub Issues: Report bugs and request features
- Documentation: Full documentation available online
- Community: Join our Discord server for help and discussions

---

**Made with ❤️ by the 200Model8 team**

*Transform your coding experience with the power of AI!*
