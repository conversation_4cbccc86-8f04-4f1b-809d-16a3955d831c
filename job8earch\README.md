# Job8earch 🔍

**AI-Powered Job Search Platform**

Job8earch is a modern job search platform that combines real job listings from Reed API with AI-powered features to help job seekers find their perfect role. Built with React and powered by OpenRouter AI models.

## ✨ Features

- **Real Job Search**: Browse live job listings from Reed API (UK's leading job board)
- **AI Chat Assistant**: Get help with career questions and general inquiries
- **Skills Analysis**: AI-powered analysis of job requirements and skills needed
- **Interview Preparation**: Get personalized interview guidance and tips
- **Job Analysis**: Detailed AI analysis of job postings
- **Multiple AI Models**: Choose from various free AI models via OpenRouter
- **API Connection Testing**: Built-in tools to verify API connectivity

## 🚀 Tech Stack

- **Frontend**: React 18, Vite, Tailwind CSS
- **AI Integration**: OpenRouter API with multiple free models
- **Job Data**: Reed API for live UK job listings
- **Styling**: Tailwind CSS with custom components
- **Build Tool**: Vite for fast development and building

## 🛠️ Setup & Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/jeff9497/Job8earch.git
   cd Job8earch
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file in the root directory:
   ```env
   VITE_REED_API_KEY=your_reed_api_key_here
   VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
   VITE_OPENROUTER_MODEL=google/gemma-3n-e2b-it:free
   ```

4. **Get API Keys**
   - **Reed API**: Get your free API key from [Reed API](https://www.reed.co.uk/developers)
   - **OpenRouter API**: Sign up at [OpenRouter](https://openrouter.ai/) and get your API key

5. **Configure OpenRouter Privacy Settings**
   - Visit [OpenRouter Privacy Settings](https://openrouter.ai/settings/privacy)
   - Enable prompt training to access free models

6. **Start development server**
   ```bash
   npm run dev
   ```

## 🔧 API Configuration

### Reed API
- **Purpose**: Fetches live job listings from the UK's leading job board
- **Get API Key**: [Reed API Developers](https://www.reed.co.uk/developers)
- **Features**: Job search, job details, salary information
- **Rate Limits**: Check Reed API documentation for current limits

### OpenRouter API
- **Purpose**: Powers AI chat, skills analysis, and interview preparation
- **Get API Key**: [OpenRouter Dashboard](https://openrouter.ai/)
- **Configure Privacy**: Enable prompt training for free model access
- **Features**: Multiple AI models, chat completions, analysis tools

## 🤖 Available AI Models

The platform supports multiple free AI models:
- Google: Gemma 3n 2B (Free)
- Tencent: Hunyuan A13B (Free)
- TNG: DeepSeek R1T2 Chimera (Free)
- Cypher Alpha (Free)
- Mistral: Small 3.2 24B (Free)

## 📱 Usage

1. **Job Search**: Search for real jobs by title, keywords, or location
2. **AI Chat**: Get career advice and general assistance
3. **Skills Analysis**: Ask AI to analyze skills for specific job titles
4. **Interview Prep**: Get personalized interview preparation guidance
5. **Job Analysis**: Get detailed AI analysis of job postings
6. **API Testing**: Use built-in connection tests to verify setup

## 🧪 Testing

The platform includes comprehensive API testing:
- **Test API Connection**: Verifies both Reed and OpenRouter APIs
- **Connection Status**: Shows real-time API connectivity
- **Error Handling**: Graceful fallbacks when APIs are unavailable
- **Debug Information**: Detailed error messages for troubleshooting

## 🚀 Deployment

```bash
npm run build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.

### Environment Variables for Production

Set these environment variables in your hosting platform:
- `VITE_REED_API_KEY`: Your Reed API key
- `VITE_OPENROUTER_API_KEY`: Your OpenRouter API key
- `VITE_OPENROUTER_MODEL`: Preferred AI model (optional)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

**Jeff Kamau**
- Email: <EMAIL>
- GitHub: [@jeff9497](https://github.com/jeff9497)

## 🙏 Acknowledgments

- [Reed API](https://www.reed.co.uk/developers) for live job data
- [OpenRouter](https://openrouter.ai/) for AI model access
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Vite](https://vitejs.dev/) for build tooling

## 🔍 Live Demo

Experience Job8earch in action: [Your Deployed URL Here]

---

*Built with ❤️ for job seekers everywhere*
