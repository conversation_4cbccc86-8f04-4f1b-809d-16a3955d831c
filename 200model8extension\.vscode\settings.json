{"files.exclude": {"out": false, "dist": true, "**/*.vsix": true}, "search.exclude": {"out": true, "dist": true, "node_modules": true, "**/*.vsix": true}, "typescript.tsc.autoDetect": "off", "typescript.preferences.includePackageJsonAutoImports": "on", "eslint.validate": ["typescript"], "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.tabSize": 2, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.suggest.autoImports": true, "typescript.preferences.importModuleSpecifier": "relative"}