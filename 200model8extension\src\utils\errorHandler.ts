import * as vscode from 'vscode';
import { logger } from './logger';

export enum ErrorType {
  NETWORK = 'network',
  API = 'api',
  FILE_SYSTEM = 'filesystem',
  PARSING = 'parsing',
  VALIDATION = 'validation',
  CONFIGURATION = 'configuration',
  UNKNOWN = 'unknown'
}

export interface ExtensionError {
  type: ErrorType;
  message: string;
  originalError?: Error;
  context?: string;
  timestamp: Date;
  recoverable: boolean;
  userMessage: string;
  suggestedActions: string[];
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorHistory: ExtensionError[] = [];
  private maxHistorySize = 100;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  public handleError(error: Error | string, context?: string, type?: ErrorType): ExtensionError {
    const extensionError = this.createExtensionError(error, context, type);
    
    // Log the error
    logger.error(`[${extensionError.type}] ${extensionError.message}`, extensionError.originalError);
    
    // Add to history
    this.addToHistory(extensionError);
    
    // Show user notification if appropriate
    this.showUserNotification(extensionError);
    
    return extensionError;
  }

  private createExtensionError(error: Error | string, context?: string, type?: ErrorType): ExtensionError {
    const message = typeof error === 'string' ? error : error.message;
    const originalError = typeof error === 'string' ? undefined : error;
    
    const errorType = type || this.detectErrorType(message, originalError);
    const { userMessage, suggestedActions, recoverable } = this.getErrorDetails(errorType, message);

    return {
      type: errorType,
      message,
      originalError,
      context,
      timestamp: new Date(),
      recoverable,
      userMessage,
      suggestedActions
    };
  }

  private detectErrorType(message: string, error?: Error): ErrorType {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('timeout')) {
      return ErrorType.NETWORK;
    }
    
    if (lowerMessage.includes('api') || lowerMessage.includes('unauthorized') || lowerMessage.includes('rate limit')) {
      return ErrorType.API;
    }
    
    if (lowerMessage.includes('file') || lowerMessage.includes('directory') || lowerMessage.includes('permission')) {
      return ErrorType.FILE_SYSTEM;
    }
    
    if (lowerMessage.includes('parse') || lowerMessage.includes('syntax') || lowerMessage.includes('invalid json')) {
      return ErrorType.PARSING;
    }
    
    if (lowerMessage.includes('config') || lowerMessage.includes('setting') || lowerMessage.includes('api key')) {
      return ErrorType.CONFIGURATION;
    }
    
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid input')) {
      return ErrorType.VALIDATION;
    }
    
    return ErrorType.UNKNOWN;
  }

  private getErrorDetails(type: ErrorType, message: string): {
    userMessage: string;
    suggestedActions: string[];
    recoverable: boolean;
  } {
    switch (type) {
      case ErrorType.NETWORK:
        return {
          userMessage: 'Network connection issue. Please check your internet connection.',
          suggestedActions: [
            'Check your internet connection',
            'Try again in a few moments',
            'Check if the AI service is available'
          ],
          recoverable: true
        };

      case ErrorType.API:
        return {
          userMessage: 'AI service error. This might be a temporary issue.',
          suggestedActions: [
            'Check your API key configuration',
            'Try switching to a different AI model',
            'Wait a moment and try again',
            'Check if you have reached rate limits'
          ],
          recoverable: true
        };

      case ErrorType.FILE_SYSTEM:
        return {
          userMessage: 'File system error. Check file permissions and paths.',
          suggestedActions: [
            'Check file and directory permissions',
            'Ensure the file path is correct',
            'Try saving to a different location',
            'Restart VS Code if the issue persists'
          ],
          recoverable: true
        };

      case ErrorType.PARSING:
        return {
          userMessage: 'Code parsing error. The code might have syntax issues.',
          suggestedActions: [
            'Check the code syntax',
            'Ensure the file language is correctly detected',
            'Try with a smaller code selection',
            'Verify the code is complete'
          ],
          recoverable: true
        };

      case ErrorType.CONFIGURATION:
        return {
          userMessage: 'Configuration error. Please check your extension settings.',
          suggestedActions: [
            'Open extension settings and verify configuration',
            'Check your Puter API key',
            'Reset settings to default values',
            'Restart VS Code after making changes'
          ],
          recoverable: true
        };

      case ErrorType.VALIDATION:
        return {
          userMessage: 'Input validation error. Please check your input.',
          suggestedActions: [
            'Verify your input is correct',
            'Check for required fields',
            'Try with different input values',
            'Refer to the documentation for valid formats'
          ],
          recoverable: true
        };

      default:
        return {
          userMessage: 'An unexpected error occurred.',
          suggestedActions: [
            'Try the operation again',
            'Restart VS Code',
            'Check the extension logs for more details',
            'Report the issue if it persists'
          ],
          recoverable: false
        };
    }
  }

  private addToHistory(error: ExtensionError): void {
    this.errorHistory.unshift(error);
    
    // Keep only the most recent errors
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  private async showUserNotification(error: ExtensionError): Promise<void> {
    const actions = ['Show Details', 'Dismiss'];
    
    if (error.suggestedActions.length > 0) {
      actions.unshift('Show Solutions');
    }

    const action = await vscode.window.showErrorMessage(
      error.userMessage,
      ...actions
    );

    switch (action) {
      case 'Show Solutions':
        await this.showSolutions(error);
        break;
      case 'Show Details':
        await this.showErrorDetails(error);
        break;
    }
  }

  private async showSolutions(error: ExtensionError): Promise<void> {
    const solutions = error.suggestedActions.map((action, index) => `${index + 1}. ${action}`).join('\n');
    
    const message = `**Suggested Solutions:**\n\n${solutions}\n\n**Error:** ${error.message}`;
    
    const doc = await vscode.workspace.openTextDocument({
      content: message,
      language: 'markdown'
    });
    
    await vscode.window.showTextDocument(doc);
  }

  private async showErrorDetails(error: ExtensionError): Promise<void> {
    const details = `**Error Details**

**Type:** ${error.type}
**Time:** ${error.timestamp.toLocaleString()}
**Context:** ${error.context || 'N/A'}
**Recoverable:** ${error.recoverable ? 'Yes' : 'No'}

**Message:** ${error.message}

**Original Error:**
${error.originalError ? error.originalError.stack || error.originalError.message : 'N/A'}

**Suggested Actions:**
${error.suggestedActions.map((action, index) => `${index + 1}. ${action}`).join('\n')}`;

    const doc = await vscode.workspace.openTextDocument({
      content: details,
      language: 'markdown'
    });
    
    await vscode.window.showTextDocument(doc);
  }

  public getErrorHistory(): ExtensionError[] {
    return [...this.errorHistory];
  }

  public clearErrorHistory(): void {
    this.errorHistory = [];
  }

  public getErrorStats(): { [key in ErrorType]: number } {
    const stats = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as { [key in ErrorType]: number });

    for (const error of this.errorHistory) {
      stats[error.type]++;
    }

    return stats;
  }

  public async showErrorReport(): Promise<void> {
    const stats = this.getErrorStats();
    const recentErrors = this.errorHistory.slice(0, 10);

    const report = `# 200Model8 Error Report

## Error Statistics
${Object.entries(stats).map(([type, count]) => `- ${type}: ${count}`).join('\n')}

## Recent Errors (Last 10)
${recentErrors.map((error, index) => `
### ${index + 1}. ${error.type} - ${error.timestamp.toLocaleString()}
**Message:** ${error.message}
**Context:** ${error.context || 'N/A'}
**Recoverable:** ${error.recoverable ? 'Yes' : 'No'}
`).join('\n')}

## Troubleshooting
If you're experiencing frequent errors:
1. Check your internet connection
2. Verify your Puter API key is correct
3. Try restarting VS Code
4. Check the extension logs for more details
5. Report persistent issues on GitHub
`;

    const doc = await vscode.workspace.openTextDocument({
      content: report,
      language: 'markdown'
    });
    
    await vscode.window.showTextDocument(doc);
  }
}

// Convenience functions
export const errorHandler = ErrorHandler.getInstance();

export function handleError(error: Error | string, context?: string, type?: ErrorType): ExtensionError {
  return errorHandler.handleError(error, context, type);
}

export function handleNetworkError(error: Error | string, context?: string): ExtensionError {
  return errorHandler.handleError(error, context, ErrorType.NETWORK);
}

export function handleAPIError(error: Error | string, context?: string): ExtensionError {
  return errorHandler.handleError(error, context, ErrorType.API);
}

export function handleFileSystemError(error: Error | string, context?: string): ExtensionError {
  return errorHandler.handleError(error, context, ErrorType.FILE_SYSTEM);
}

export function handleConfigurationError(error: Error | string, context?: string): ExtensionError {
  return errorHandler.handleError(error, context, ErrorType.CONFIGURATION);
}
