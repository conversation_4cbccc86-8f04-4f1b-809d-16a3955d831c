// Type definitions for Puter.js integration
declare module 'puter' {
  export interface PuterConfig {
    apiKey?: string;
    baseUrl?: string;
    timeout?: number;
    retries?: number;
  }

  export interface PuterModel {
    id: string;
    name: string;
    provider: string;
    maxTokens: number;
    costPer1kTokens: number;
    capabilities: string[];
  }

  export interface PuterRequest {
    model: string;
    messages: PuterMessage[];
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    stream?: boolean;
    stop?: string[];
  }

  export interface PuterMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    name?: string;
  }

  export interface PuterResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: PuterChoice[];
    usage: PuterUsage;
  }

  export interface PuterChoice {
    index: number;
    message: PuterMessage;
    finishReason: string;
  }

  export interface PuterUsage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  }

  export interface PuterStreamChunk {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: PuterStreamChoice[];
  }

  export interface PuterStreamChoice {
    index: number;
    delta: {
      role?: string;
      content?: string;
    };
    finishReason?: string;
  }

  export interface PuterError {
    error: {
      message: string;
      type: string;
      code: string;
    };
  }

  export class PuterClient {
    constructor(config: PuterConfig);
    
    chat: {
      completions: {
        create(request: PuterRequest): Promise<PuterResponse>;
        createStream(request: PuterRequest): AsyncIterable<PuterStreamChunk>;
      };
    };
    
    models: {
      list(): Promise<PuterModel[]>;
      retrieve(modelId: string): Promise<PuterModel>;
    };
  }

  export default PuterClient;
}

// Additional types for our extension's Puter integration
export interface PuterServiceConfig {
  apiKey: string;
  defaultModel: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  retries: number;
}

export interface PuterContextMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  metadata?: {
    filePath?: string;
    language?: string;
    timestamp?: Date;
    type?: 'code' | 'explanation' | 'instruction';
  };
}

export interface PuterCodeRequest {
  prompt: string;
  language: string;
  context?: {
    filePath?: string;
    existingCode?: string;
    projectInfo?: any;
    dependencies?: string[];
  };
  options?: {
    includeComments?: boolean;
    includeTests?: boolean;
    style?: 'functional' | 'object-oriented' | 'mixed';
    complexity?: 'simple' | 'moderate' | 'complex';
  };
}

export interface PuterCodeResponse {
  code: string;
  explanation: string;
  suggestions: string[];
  dependencies: string[];
  tests?: string;
  documentation?: string;
}

export interface PuterProjectRequest {
  description: string;
  language: string;
  framework?: string;
  features: string[];
  structure?: 'monorepo' | 'single' | 'microservices';
  database?: string;
  authentication?: boolean;
  testing?: boolean;
}

export interface PuterProjectResponse {
  name: string;
  structure: {
    directories: string[];
    files: Array<{
      path: string;
      content: string;
      type: string;
    }>;
  };
  dependencies: Array<{
    name: string;
    version: string;
    type: 'production' | 'development';
  }>;
  scripts: Record<string, string>;
  configuration: Record<string, any>;
  documentation: string;
}

export interface PuterAnalysisRequest {
  code: string;
  language: string;
  analysisType: 'bugs' | 'performance' | 'security' | 'style' | 'complexity';
  context?: {
    filePath?: string;
    projectType?: string;
    framework?: string;
  };
}

export interface PuterAnalysisResponse {
  analysis: {
    score: number;
    issues: Array<{
      type: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      message: string;
      line?: number;
      column?: number;
      suggestion?: string;
      fixCode?: string;
    }>;
    suggestions: string[];
    metrics: {
      complexity: number;
      maintainability: number;
      performance: number;
      security: number;
    };
  };
  recommendations: string[];
}

export interface PuterRefactorRequest {
  code: string;
  language: string;
  instructions: string;
  context?: {
    filePath?: string;
    projectInfo?: any;
    constraints?: string[];
  };
  options?: {
    preserveComments?: boolean;
    maintainAPI?: boolean;
    optimizePerformance?: boolean;
  };
}

export interface PuterRefactorResponse {
  refactoredCode: string;
  changes: Array<{
    type: 'added' | 'removed' | 'modified';
    description: string;
    lineNumber?: number;
    oldCode?: string;
    newCode?: string;
  }>;
  explanation: string;
  benefits: string[];
  warnings?: string[];
}

export interface PuterTestRequest {
  code: string;
  language: string;
  testFramework?: string;
  testType: 'unit' | 'integration' | 'e2e';
  coverage?: 'basic' | 'comprehensive';
  context?: {
    filePath?: string;
    dependencies?: string[];
    mockingStrategy?: string;
  };
}

export interface PuterTestResponse {
  tests: string;
  testFiles: Array<{
    path: string;
    content: string;
  }>;
  setup?: string;
  dependencies: string[];
  coverage: {
    estimated: number;
    areas: string[];
  };
  explanation: string;
}
